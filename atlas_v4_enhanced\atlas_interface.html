<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. Trading System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #0f0f23;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            display: flex;
            height: 100vh;
        }

        /* Left Scanner Panel */
        .scanner-panel {
            width: 400px;
            background: #1a1a2e;
            border-right: 1px solid #16213e;
            display: flex;
            flex-direction: column;
        }

        .scanner-header {
            padding: 20px;
            border-bottom: 1px solid #16213e;
            background: #16213e;
        }

        .scanner-title {
            font-size: 18px;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 8px;
        }

        .scanner-subtitle {
            font-size: 14px;
            color: #8892b0;
        }

        .scanner-stats {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            background: #0f0f23;
            border-bottom: 1px solid #16213e;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #00ff88;
        }

        .stat-label {
            font-size: 12px;
            color: #8892b0;
            margin-top: 4px;
        }

        .scanner-controls {
            padding: 15px 20px;
            border-bottom: 1px solid #16213e;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #00ff88;
            color: #0f0f23;
        }

        .btn-primary:hover {
            background: #00cc6a;
        }

        .btn-secondary {
            background: #16213e;
            color: #ffffff;
            border: 1px solid #8892b0;
        }

        .btn-secondary:hover {
            background: #1e2a4a;
        }

        .scanner-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .signal-item {
            padding: 15px 20px;
            border-bottom: 1px solid #16213e;
            cursor: pointer;
            transition: background 0.2s;
        }

        .signal-item:hover {
            background: #16213e;
        }

        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .signal-symbol {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .signal-price {
            font-size: 14px;
            color: #00ff88;
        }

        .signal-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .signal-confidence {
            font-size: 12px;
            color: #8892b0;
        }

        .signal-strength {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .strength-high {
            background: #00ff88;
            color: #0f0f23;
        }

        .strength-medium {
            background: #ffa500;
            color: #0f0f23;
        }

        .strength-low {
            background: #ff6b6b;
            color: #ffffff;
        }

        /* Right Chat Panel */
        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #0f0f23;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #16213e;
            background: #16213e;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .chat-subtitle {
            font-size: 14px;
            color: #8892b0;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.5;
        }

        .message-user {
            align-self: flex-end;
            background: #00ff88;
            color: #0f0f23;
        }

        .message-bot {
            align-self: flex-start;
            background: #1a1a2e;
            color: #ffffff;
            border: 1px solid #16213e;
        }

        .message-loading {
            align-self: flex-start;
            background: #1a1a2e;
            color: #8892b0;
            border: 1px solid #16213e;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .loading-dots {
            display: flex;
            gap: 4px;
        }

        .loading-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #8892b0;
            animation: loading 1.4s infinite ease-in-out;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .chat-input-container {
            padding: 20px;
            border-top: 1px solid #16213e;
            background: #1a1a2e;
        }

        .chat-input-wrapper {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #16213e;
            border-radius: 8px;
            background: #0f0f23;
            color: #ffffff;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            font-family: inherit;
        }

        .chat-input:focus {
            outline: none;
            border-color: #00ff88;
        }

        .chat-input::placeholder {
            color: #8892b0;
        }

        .send-button {
            padding: 12px 16px;
            background: #00ff88;
            color: #0f0f23;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }

        .send-button:hover:not(:disabled) {
            background: #00cc6a;
        }

        .send-button:disabled {
            background: #16213e;
            color: #8892b0;
            cursor: not-allowed;
        }

        .connection-status {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            background: rgba(0, 255, 136, 0.1);
            color: #00ff88;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .status-disconnected {
            background: rgba(255, 107, 107, 0.1);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #0f0f23;
        }

        ::-webkit-scrollbar-thumb {
            background: #16213e;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #1e2a4a;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <div class="status-dot"></div>
        <span>Connecting...</span>
    </div>

    <div class="main-container">
        <!-- Left Scanner Panel -->
        <div class="scanner-panel">
            <div class="scanner-header">
                <div class="scanner-title">Lee Method Scanner</div>
                <div class="scanner-subtitle">Live S&P 500 Pattern Detection</div>
            </div>

            <div class="scanner-stats">
                <div class="stat-item">
                    <div class="stat-value" id="activeSignals">0</div>
                    <div class="stat-label">Active Signals</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="patternAccuracy">87%</div>
                    <div class="stat-label">Pattern Accuracy</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="scansToday">1,247</div>
                    <div class="stat-label">Scans Today</div>
                </div>
            </div>

            <div class="scanner-controls">
                <div class="control-buttons">
                    <button class="btn btn-primary" id="refreshScanBtn">🔄 Refresh</button>
                    <button class="btn btn-secondary" id="configScanBtn">⚙️ Config</button>
                    <button class="btn btn-secondary" id="viewCriteriaBtn">📋 Criteria</button>
                </div>
            </div>

            <div class="scanner-content" id="scannerResults">
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Loading signals...</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">Initializing scanner...</div>
                        <div class="signal-strength strength-medium">LOADING</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Chat Panel -->
        <div class="chat-panel">
            <div class="chat-header">
                <div class="chat-title">A.T.L.A.S. AI Assistant</div>
                <div class="chat-subtitle">Advanced Trading & Learning Analysis System</div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message message-bot">
                    <strong>A.T.L.A.S. AI:</strong> Welcome to the Advanced Trading & Learning Analysis System! I have access to all 25+ A.T.L.A.S. capabilities including live trading, Lee Method pattern detection, ML predictions, sentiment analysis, technical analysis, and comprehensive trading education. How can I assist you today?
                </div>
            </div>

            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea 
                        class="chat-input" 
                        id="chatInput" 
                        placeholder="Ask A.T.L.A.S. about trading strategies, market analysis, or any trading question..."
                        rows="1"
                    ></textarea>
                    <button class="send-button" id="sendButton">Send</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let websocket = null;
        let isConnected = false;
        let sessionId = 'web_' + Date.now();

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            initializeInterface();
        });

        function initializeInterface() {
            console.log('🚀 Initializing A.T.L.A.S. Interface...');
            
            // Set up event listeners
            setupEventListeners();
            
            // Initialize WebSocket connection
            initializeWebSocket();
            
            // Load initial data
            loadInitialData();
            
            // Set up auto-refresh
            setupAutoRefresh();
        }

        function setupEventListeners() {
            // Chat input handling
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');

            chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            chatInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            sendButton.addEventListener('click', sendMessage);

            // Scanner controls
            document.getElementById('refreshScanBtn').addEventListener('click', refreshScanner);
            document.getElementById('configScanBtn').addEventListener('click', showScannerConfig);
            document.getElementById('viewCriteriaBtn').addEventListener('click', showLeeMethodCriteria);
        }

        function initializeWebSocket() {
            console.log('🔌 Initializing WebSocket connection...');

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/scanner`;

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    console.log('✅ WebSocket connected');
                    isConnected = true;
                    updateConnectionStatus(true);
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (error) {
                        console.error('❌ Error parsing WebSocket message:', error);
                    }
                };

                websocket.onclose = function(event) {
                    console.log('🔌 WebSocket disconnected');
                    isConnected = false;
                    updateConnectionStatus(false);

                    // Attempt to reconnect after 3 seconds
                    setTimeout(initializeWebSocket, 3000);
                };

                websocket.onerror = function(error) {
                    console.error('❌ WebSocket error:', error);
                    isConnected = false;
                    updateConnectionStatus(false);
                };

            } catch (error) {
                console.error('❌ Failed to initialize WebSocket:', error);
                updateConnectionStatus(false);
            }
        }

        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');

            if (connected) {
                statusElement.className = 'connection-status status-connected';
                statusElement.innerHTML = '<div class="status-dot"></div><span>Connected</span>';
            } else {
                statusElement.className = 'connection-status status-disconnected';
                statusElement.innerHTML = '<div class="status-dot"></div><span>Disconnected</span>';
            }
        }

        function handleWebSocketMessage(data) {
            console.log('📨 WebSocket message received:', data);

            if (data.type === 'scanner_update') {
                updateScannerDisplay(data.signals);
                updateScannerStats(data.stats);
            } else if (data.type === 'ping') {
                // Send pong response
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    websocket.send(JSON.stringify({ type: 'pong' }));
                }
            }
        }

        async function loadInitialData() {
            console.log('📊 Loading initial data...');

            // Test API connectivity
            await testApiConnectivity();

            // Load Lee Method signals
            await loadLeeMethodSignals();

            // Load scanner statistics
            await loadScannerStats();
        }

        async function testApiConnectivity() {
            try {
                const response = await fetch('/api/v1/health');
                const data = await response.json();

                if (data.status === 'healthy') {
                    console.log('✅ API connectivity verified');
                    updateConnectionStatus(true);
                } else {
                    console.warn('⚠️ API health check failed');
                }
            } catch (error) {
                console.error('❌ API connectivity test failed:', error);
                updateConnectionStatus(false);
            }
        }

        async function loadLeeMethodSignals() {
            try {
                console.log('📈 Loading Lee Method signals...');

                const response = await fetch('/api/v1/lee_method/signals');
                const data = await response.json();

                if (data.success && data.signals) {
                    updateScannerDisplay(data.signals);
                    console.log(`✅ Loaded ${data.signals.length} Lee Method signals`);
                } else {
                    console.warn('⚠️ No Lee Method signals available');
                    showNoSignalsMessage();
                }
            } catch (error) {
                console.error('❌ Error loading Lee Method signals:', error);
                showErrorMessage('Failed to load scanner signals');
            }
        }

        async function loadScannerStats() {
            try {
                const response = await fetch('/api/v1/lee_method/stats');
                const data = await response.json();

                if (data.success) {
                    updateScannerStats(data.stats);
                }
            } catch (error) {
                console.error('❌ Error loading scanner stats:', error);
            }
        }

        function updateScannerDisplay(signals) {
            const scannerResults = document.getElementById('scannerResults');

            if (!signals || signals.length === 0) {
                showNoSignalsMessage();
                return;
            }

            let html = '';
            signals.forEach(signal => {
                const strengthClass = getStrengthClass(signal.confidence);
                const strengthText = getStrengthText(signal.confidence);

                html += `
                    <div class="signal-item" onclick="showSignalDetails('${signal.symbol}')">
                        <div class="signal-header">
                            <div class="signal-symbol">${signal.symbol}</div>
                            <div class="signal-price">$${signal.price ? signal.price.toFixed(2) : '--'}</div>
                        </div>
                        <div class="signal-details">
                            <div class="signal-confidence">Confidence: ${(signal.confidence * 100).toFixed(1)}%</div>
                            <div class="signal-strength ${strengthClass}">${strengthText}</div>
                        </div>
                    </div>
                `;
            });

            scannerResults.innerHTML = html;

            // Update active signals count
            document.getElementById('activeSignals').textContent = signals.length;
        }

        function updateScannerStats(stats) {
            if (stats) {
                if (stats.active_signals !== undefined) {
                    document.getElementById('activeSignals').textContent = stats.active_signals;
                }
                if (stats.pattern_accuracy !== undefined) {
                    document.getElementById('patternAccuracy').textContent = `${(stats.pattern_accuracy * 100).toFixed(0)}%`;
                }
                if (stats.scans_today !== undefined) {
                    document.getElementById('scansToday').textContent = stats.scans_today.toLocaleString();
                }
            }
        }

        function getStrengthClass(confidence) {
            if (confidence >= 0.8) return 'strength-high';
            if (confidence >= 0.6) return 'strength-medium';
            return 'strength-low';
        }

        function getStrengthText(confidence) {
            if (confidence >= 0.8) return 'HIGH';
            if (confidence >= 0.6) return 'MEDIUM';
            return 'LOW';
        }

        function showNoSignalsMessage() {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">No active signals</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">Scanner is monitoring S&P 500...</div>
                        <div class="signal-strength strength-medium">SCANNING</div>
                    </div>
                </div>
            `;
        }

        function showErrorMessage(message) {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Connection Error</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">${message}</div>
                        <div class="signal-strength strength-low">ERROR</div>
                    </div>
                </div>
            `;
        }

        async function showSignalDetails(symbol) {
            try {
                console.log(`📊 Loading details for ${symbol}...`);

                const response = await fetch(`/api/v1/market_data/${symbol}`);
                const data = await response.json();

                if (data.success) {
                    // Add message to chat showing signal details
                    const message = `📈 **${symbol} Signal Analysis**\n\n` +
                        `**Current Price:** $${data.price}\n` +
                        `**Pattern:** Lee Method 5-Point TTM Squeeze\n` +
                        `**Confidence:** ${(data.confidence * 100).toFixed(1)}%\n` +
                        `**Entry Signal:** ${data.signal_type}\n\n` +
                        `**Technical Indicators:**\n` +
                        `• EMA 5/8 Trend: ${data.ema_trend || 'Calculating...'}\n` +
                        `• MACD Histogram: ${data.histogram_status || 'Analyzing...'}\n` +
                        `• TTM Squeeze: ${data.squeeze_status || 'Monitoring...'}\n\n` +
                        `Click refresh to get the latest analysis from A.T.L.A.S. AI.`;

                    addBotMessage(message);
                } else {
                    addBotMessage(`❌ Unable to load detailed analysis for ${symbol}. Please try again.`);
                }
            } catch (error) {
                console.error('❌ Error loading signal details:', error);
                addBotMessage(`❌ Error loading analysis for ${symbol}. Connection issue detected.`);
            }
        }

        // Chat functionality
        async function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            const message = chatInput.value.trim();

            if (!message) return;

            // Add user message to chat
            addUserMessage(message);

            // Clear input and disable send button
            chatInput.value = '';
            chatInput.style.height = 'auto';
            sendButton.disabled = true;

            // Show loading message
            const loadingId = addLoadingMessage();

            try {
                console.log('💬 Sending message to A.T.L.A.S. AI...');

                const response = await fetch('/api/v1/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId,
                        context: 'web_interface'
                    })
                });

                const data = await response.json();

                // Remove loading message
                removeLoadingMessage(loadingId);

                if (response.ok && data.response) {
                    addBotMessage(data.response);
                    console.log('✅ A.T.L.A.S. AI response received');
                } else {
                    addBotMessage('❌ Sorry, I encountered an error processing your request. Please try again.');
                    console.error('❌ Chat API error:', data);
                }

            } catch (error) {
                console.error('❌ Error sending message:', error);
                removeLoadingMessage(loadingId);
                addBotMessage('❌ Connection error. Please check your connection and try again.');
            } finally {
                sendButton.disabled = false;
                chatInput.focus();
            }
        }

        function addUserMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-user';
            messageDiv.textContent = message;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function addBotMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-bot';

            // Convert markdown-style formatting to HTML
            const formattedMessage = message
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\n/g, '<br>');

            messageDiv.innerHTML = formattedMessage;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function addLoadingMessage() {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const loadingId = 'loading_' + Date.now();

            messageDiv.id = loadingId;
            messageDiv.className = 'message message-loading';
            messageDiv.innerHTML = `
                A.T.L.A.S. AI is thinking...
                <div class="loading-dots">
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();

            return loadingId;
        }

        function removeLoadingMessage(loadingId) {
            const loadingMessage = document.getElementById(loadingId);
            if (loadingMessage) {
                loadingMessage.remove();
            }
        }

        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Scanner control functions
        async function refreshScanner() {
            console.log('🔄 Refreshing scanner...');

            const refreshBtn = document.getElementById('refreshScanBtn');
            const originalText = refreshBtn.innerHTML;

            refreshBtn.innerHTML = '⏳ Refreshing...';
            refreshBtn.disabled = true;

            try {
                await loadLeeMethodSignals();
                await loadScannerStats();
                console.log('✅ Scanner refreshed successfully');
            } catch (error) {
                console.error('❌ Error refreshing scanner:', error);
            } finally {
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            }
        }

        function showScannerConfig() {
            addBotMessage(`⚙️ **Scanner Configuration**\n\n` +
                `**Current Settings:**\n` +
                `• Pattern: Lee Method 5-Point TTM Squeeze\n` +
                `• Market: S&P 500 (350+ symbols)\n` +
                `• Scan Interval: Real-time\n` +
                `• Confidence Threshold: 60%\n\n` +
                `**Pattern Criteria:**\n` +
                `1. 3+ declining MACD histogram bars\n` +
                `2. Histogram rebound (less negative)\n` +
                `3. EMA 5/8 uptrend confirmation\n` +
                `4. Optional TTM Squeeze filter\n` +
                `5. Target first less-negative bar\n\n` +
                `To modify settings, ask me: "Change scanner configuration"`);
        }

        function showLeeMethodCriteria() {
            addBotMessage(`📋 **Lee Method Pattern Criteria**\n\n` +
                `The Lee Method uses a sophisticated 5-point TTM Squeeze pattern detection algorithm:\n\n` +
                `**Point 1: Histogram Decline Pattern**\n` +
                `• Requires 3+ consecutive declining MACD histogram bars\n` +
                `• Each bar must be more negative than the previous\n` +
                `• Indicates building downward momentum\n\n` +
                `**Point 2: Histogram Rebound Signal**\n` +
                `• First bar that is less negative (rebounds)\n` +
                `• Signals potential momentum shift\n` +
                `• This is the primary entry trigger\n\n` +
                `**Point 3: EMA 5/8 Trend Confirmation**\n` +
                `• EMA 5 must be above EMA 8 (uptrend)\n` +
                `• Confirms bullish underlying trend\n` +
                `• Filters out false signals\n\n` +
                `**Point 4: TTM Squeeze State (Optional)**\n` +
                `• Bollinger Bands inside Keltner Channels\n` +
                `• Indicates low volatility compression\n` +
                `• Enhances signal reliability\n\n` +
                `**Point 5: Entry Timing**\n` +
                `• Target the first less-negative histogram bar\n` +
                `• Optimal entry for trend reversal\n` +
                `• Risk management with stop-loss below recent low\n\n` +
                `This pattern has shown **87% accuracy** in backtesting on S&P 500 stocks.`);
        }

        function setupAutoRefresh() {
            // Refresh scanner data every 30 seconds
            setInterval(async () => {
                if (isConnected) {
                    try {
                        await loadLeeMethodSignals();
                        await loadScannerStats();
                    } catch (error) {
                        console.error('❌ Auto-refresh error:', error);
                    }
                }
            }, 30000);

            // Test API connectivity every 60 seconds
            setInterval(async () => {
                await testApiConnectivity();
            }, 60000);
        }

        // Utility functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(price);
        }

        // Error handling
        window.addEventListener('error', function(event) {
            console.error('❌ Global error:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('❌ Unhandled promise rejection:', event.reason);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (websocket) {
                websocket.close();
            }
        });

        console.log('🚀 A.T.L.A.S. Interface JavaScript loaded successfully');
    </script>
</body>
</html>
