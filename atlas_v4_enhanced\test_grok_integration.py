#!/usr/bin/env python3
"""
Test Grok AI Integration in A.T.L.A.S. Trading System
Tests the new Grok AI functionality and ensures compatibility
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from config import get_api_config, settings
from atlas_ai_core import AtlasAIEngine
from atlas_orchestrator import AtlasOrchestrator

async def test_grok_configuration():
    """Test Grok AI configuration"""
    print("🔧 Testing Grok AI Configuration...")
    
    grok_config = get_api_config("grok")
    print(f"   ✓ Grok API Key: {'✓ Present' if grok_config.get('api_key') else '✗ Missing'}")
    print(f"   ✓ Grok Base URL: {grok_config.get('base_url', 'Not set')}")
    print(f"   ✓ Grok Model: {grok_config.get('model', 'Not set')}")
    print(f"   ✓ Grok Available: {grok_config.get('available', False)}")
    
    return grok_config.get('available', False)

async def test_ai_engine_initialization():
    """Test AI engine initialization with Grok"""
    print("\n🤖 Testing AI Engine Initialization...")
    
    try:
        ai_engine = AtlasAIEngine()
        await ai_engine.initialize()
        
        print(f"   ✓ AI Engine Status: {ai_engine.status}")
        print(f"   ✓ Atlas Engine Status: {ai_engine.atlas_engine.status}")
        
        # Check if Grok client is initialized
        has_grok = hasattr(ai_engine.atlas_engine, '_grok_client') and ai_engine.atlas_engine._grok_client is not None
        has_openai = hasattr(ai_engine.atlas_engine, '_openai_client') and ai_engine.atlas_engine._openai_client is not None
        
        print(f"   ✓ Grok Client: {'✓ Initialized' if has_grok else '✗ Not initialized'}")
        print(f"   ✓ OpenAI Client (Fallback): {'✓ Initialized' if has_openai else '✗ Not initialized'}")
        
        return ai_engine
        
    except Exception as e:
        print(f"   ✗ AI Engine initialization failed: {e}")
        return None

async def test_grok_api_call():
    """Test direct Grok API call"""
    print("\n🌐 Testing Direct Grok API Call...")
    
    try:
        ai_engine = AtlasAIEngine()
        await ai_engine.initialize()
        
        if hasattr(ai_engine.atlas_engine, '_grok_client') and ai_engine.atlas_engine._grok_client:
            test_messages = [
                {"role": "system", "content": "You are a helpful trading assistant."},
                {"role": "user", "content": "Say 'Grok AI integration successful' if you can read this."}
            ]
            
            response = await ai_engine.atlas_engine._call_grok_api(test_messages)
            
            if response:
                print(f"   ✓ Grok API Response: {response[:100]}...")
                return True
            else:
                print("   ✗ Grok API returned no response")
                return False
        else:
            print("   ✗ Grok client not available")
            return False
            
    except Exception as e:
        print(f"   ✗ Grok API call failed: {e}")
        return False

async def test_message_processing():
    """Test message processing with Grok AI"""
    print("\n💬 Testing Message Processing...")
    
    try:
        orchestrator = AtlasOrchestrator()
        await orchestrator.initialize()
        
        test_messages = [
            "Hello, what can you do?",
            "What is a stock?",
            "Analyze AAPL stock"
        ]
        
        for message in test_messages:
            print(f"\n   Testing: '{message}'")
            
            response = await orchestrator.process_message(message, "test_session")
            
            if response and response.get('response'):
                print(f"   ✓ Response received: {response['response'][:100]}...")
                print(f"   ✓ Response type: {response.get('type', 'unknown')}")
                print(f"   ✓ Confidence: {response.get('confidence', 0)}")
                
                # Check if response indicates Grok was used
                metadata = response.get('context', {})
                ai_provider = metadata.get('ai_provider', 'unknown')
                print(f"   ✓ AI Provider: {ai_provider}")
            else:
                print(f"   ✗ No response received")
                
    except Exception as e:
        print(f"   ✗ Message processing failed: {e}")

async def test_scanner_integration():
    """Test scanner integration with Grok analysis"""
    print("\n🔍 Testing Scanner Integration...")
    
    try:
        ai_engine = AtlasAIEngine()
        await ai_engine.initialize()
        
        # Mock scanner results for testing
        mock_scanner_results = {
            "signals": [
                {
                    "symbol": "AAPL",
                    "confidence": 0.85,
                    "signal_direction": "bullish",
                    "entry_price": 150.00,
                    "target_price": 160.00,
                    "stop_loss": 145.00
                }
            ],
            "scan_time": datetime.now().isoformat(),
            "total_symbols_scanned": 500
        }
        
        analysis = await ai_engine.atlas_engine.analyze_scanner_results(
            mock_scanner_results,
            "Test scanner results for Grok analysis"
        )
        
        if analysis and "unavailable" not in analysis.lower():
            print(f"   ✓ Scanner analysis received: {analysis[:150]}...")
            return True
        else:
            print(f"   ✗ Scanner analysis failed or unavailable: {analysis}")
            return False
            
    except Exception as e:
        print(f"   ✗ Scanner integration test failed: {e}")
        return False

async def test_6_point_analysis_format():
    """Test that 6-point analysis format is maintained"""
    print("\n📊 Testing 6-Point Analysis Format...")
    
    try:
        orchestrator = AtlasOrchestrator()
        await orchestrator.initialize()
        
        # Test stock analysis request
        response = await orchestrator.process_message("Analyze TSLA stock", "test_session")
        
        if response and response.get('response'):
            response_text = response['response']
            
            # Check for 6-point analysis elements
            analysis_elements = [
                "technical", "fundamental", "sentiment", "risk", "recommendation", "target"
            ]
            
            found_elements = []
            for element in analysis_elements:
                if element.lower() in response_text.lower():
                    found_elements.append(element)
            
            print(f"   ✓ Analysis elements found: {found_elements}")
            print(f"   ✓ Format compatibility: {'✓ Maintained' if len(found_elements) >= 3 else '✗ Compromised'}")
            
            return len(found_elements) >= 3
        else:
            print("   ✗ No analysis response received")
            return False
            
    except Exception as e:
        print(f"   ✗ 6-point analysis test failed: {e}")
        return False

async def main():
    """Run all Grok integration tests"""
    print("🚀 A.T.L.A.S. Grok AI Integration Test Suite")
    print("=" * 50)
    
    test_results = []
    
    # Run all tests
    test_results.append(await test_grok_configuration())
    
    ai_engine = await test_ai_engine_initialization()
    test_results.append(ai_engine is not None)
    
    test_results.append(await test_grok_api_call())
    
    await test_message_processing()  # This test is informational
    
    test_results.append(await test_scanner_integration())
    test_results.append(await test_6_point_analysis_format())
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"   ✓ Tests Passed: {passed}/{total}")
    print(f"   ✓ Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("   🎉 All tests passed! Grok AI integration successful.")
    elif passed >= total * 0.8:
        print("   ⚠️  Most tests passed. Minor issues may need attention.")
    else:
        print("   ❌ Multiple test failures. Integration needs work.")
    
    print("\n💡 Note: Some tests may fail if Grok API credits are not available.")
    print("   The integration code is ready and will work once credits are added.")

if __name__ == "__main__":
    asyncio.run(main())
