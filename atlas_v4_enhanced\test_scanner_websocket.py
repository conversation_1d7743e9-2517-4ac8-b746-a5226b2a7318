#!/usr/bin/env python3
"""
Test script to verify A.T.L.A.S. real-time scanner WebSocket functionality
"""

import asyncio
import websockets
import json
import sys

async def test_scanner_websocket():
    """Test WebSocket connection to scanner"""
    uri = "ws://localhost:8001/ws/scanner"
    
    try:
        print("🔌 Connecting to A.T.L.A.S. Scanner WebSocket...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected successfully!")
            
            # Send ping
            ping_message = {"type": "ping"}
            await websocket.send(json.dumps(ping_message))
            print("📤 Sent ping message")
            
            # Listen for messages for 30 seconds
            print("👂 Listening for scanner updates (30 seconds)...")
            
            try:
                async with asyncio.timeout(30):
                    while True:
                        message = await websocket.recv()
                        data = json.loads(message)
                        
                        if data.get('type') == 'pong':
                            print("📥 Received pong response")
                        elif data.get('type') == 'scanner_update':
                            print(f"🎯 Scanner Update: {data['data']['symbol']} - {data['data']['pattern_type']} (confidence: {data['data']['confidence']:.2f})")
                        else:
                            print(f"📨 Received: {data}")
                            
            except asyncio.TimeoutError:
                print("⏰ Test completed (30 second timeout)")
                
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False
    
    return True

async def test_scanner_api():
    """Test scanner API endpoints"""
    import aiohttp
    
    print("\n🔍 Testing Scanner API Endpoints...")
    
    async with aiohttp.ClientSession() as session:
        # Test status endpoint
        try:
            async with session.get('http://localhost:8001/api/v1/scanner/status') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Status: Running={data['running']}, Scans={data['scan_count']}, API Calls={data['api_calls_per_minute']}")
                else:
                    print(f"❌ Status endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Status test failed: {e}")
        
        # Test results endpoint
        try:
            async with session.get('http://localhost:8001/api/v1/scanner/results') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Results: {len(data['results'])} active signals")
                    for result in data['results'][:3]:  # Show first 3 results
                        print(f"   📊 {result['symbol']}: {result['pattern_type']} ({result['confidence']:.2f})")
                else:
                    print(f"❌ Results endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Results test failed: {e}")

async def main():
    """Main test function"""
    print("🚀 A.T.L.A.S. Real-Time Scanner Test Suite")
    print("=" * 50)
    
    # Test API endpoints first
    await test_scanner_api()
    
    # Test WebSocket connection
    print("\n🌐 Testing WebSocket Connection...")
    success = await test_scanner_websocket()
    
    if success:
        print("\n✅ All tests completed successfully!")
        print("🎯 Scanner is working correctly and ready for frontend integration")
    else:
        print("\n❌ Some tests failed - check server logs for details")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(0)
