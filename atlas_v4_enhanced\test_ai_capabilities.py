#!/usr/bin/env python3
"""
Test script to verify A.T.L.A.S. AI capability awareness
Tests various trading-related requests to ensure the AI recognizes and responds appropriately
"""

import asyncio
import aiohttp
import json
import sys
from typing import Dict, Any

# Test cases for different A.T.L.A.S. capabilities
TEST_CASES = [
    {
        "name": "General Capabilities Query",
        "message": "What can you do?",
        "expected_keywords": ["trading", "analysis", "lee method", "ml", "risk", "sentiment"]
    },
    {
        "name": "Live Trading Request",
        "message": "I want to buy 100 shares of AAPL",
        "expected_keywords": ["trading", "aapl", "execute", "order", "risk"]
    },
    {
        "name": "Lee Method Pattern Detection",
        "message": "Can you scan for Lee Method patterns in TSLA?",
        "expected_keywords": ["lee method", "pattern", "tsla", "momentum", "histogram"]
    },
    {
        "name": "ML Prediction Request",
        "message": "What's your LSTM price prediction for NVDA?",
        "expected_keywords": ["lstm", "prediction", "nvda", "neural network", "forecast"]
    },
    {
        "name": "Risk Assessment Request",
        "message": "What's the risk of investing $10000 in MSFT?",
        "expected_keywords": ["risk", "msft", "assessment", "position", "var"]
    },
    {
        "name": "Sentiment Analysis Request",
        "message": "What's the market sentiment for GOOGL?",
        "expected_keywords": ["sentiment", "googl", "analysis", "news", "market"]
    },
    {
        "name": "6-Point Analysis Request",
        "message": "Give me a 6-point analysis of SPY",
        "expected_keywords": ["6-point", "spy", "analysis", "trading", "recommendation"]
    }
]

async def test_chat_endpoint(session: aiohttp.ClientSession, test_case: Dict[str, Any]) -> Dict[str, Any]:
    """Test a single chat endpoint request"""
    try:
        payload = {
            "message": test_case["message"],
            "session_id": f"test_session_{test_case['name'].replace(' ', '_').lower()}"
        }
        
        async with session.post("http://localhost:8080/api/v1/chat", json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return {
                    "test_name": test_case["name"],
                    "success": True,
                    "response": data.get("response", ""),
                    "type": data.get("type", ""),
                    "confidence": data.get("confidence", 0.0),
                    "expected_keywords": test_case["expected_keywords"]
                }
            else:
                return {
                    "test_name": test_case["name"],
                    "success": False,
                    "error": f"HTTP {response.status}",
                    "expected_keywords": test_case["expected_keywords"]
                }
    except Exception as e:
        return {
            "test_name": test_case["name"],
            "success": False,
            "error": str(e),
            "expected_keywords": test_case["expected_keywords"]
        }

def analyze_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze the AI response for capability awareness"""
    if not result["success"]:
        return {
            "capability_awareness": False,
            "keyword_matches": 0,
            "total_keywords": len(result["expected_keywords"]),
            "analysis": f"Request failed: {result.get('error', 'Unknown error')}"
        }
    
    response_text = result["response"].lower()
    expected_keywords = result["expected_keywords"]
    
    # Check for keyword matches
    keyword_matches = sum(1 for keyword in expected_keywords if keyword.lower() in response_text)
    
    # Check for negative responses that indicate lack of capability awareness
    negative_indicators = [
        "i cannot", "i can't", "i don't have access", "not available", 
        "unable to", "cannot perform", "don't have the ability"
    ]
    
    has_negative_indicators = any(indicator in response_text for indicator in negative_indicators)
    
    # Determine capability awareness
    capability_awareness = (
        keyword_matches >= len(expected_keywords) * 0.5 and  # At least 50% keyword match
        not has_negative_indicators and  # No negative capability indicators
        len(response_text) > 50  # Substantial response
    )
    
    return {
        "capability_awareness": capability_awareness,
        "keyword_matches": keyword_matches,
        "total_keywords": len(expected_keywords),
        "has_negative_indicators": has_negative_indicators,
        "response_length": len(response_text),
        "analysis": "Good capability awareness" if capability_awareness else "Poor capability awareness"
    }

async def run_capability_tests():
    """Run all capability awareness tests"""
    print("🧪 Testing A.T.L.A.S. AI Capability Awareness")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        results = []
        
        for i, test_case in enumerate(TEST_CASES, 1):
            print(f"\n{i}. Testing: {test_case['name']}")
            print(f"   Query: {test_case['message']}")
            
            result = await test_chat_endpoint(session, test_case)
            analysis = analyze_response(result)
            
            results.append({**result, **analysis})
            
            if result["success"]:
                print(f"   ✅ Response received (confidence: {result['confidence']:.2f})")
                print(f"   📊 Keyword matches: {analysis['keyword_matches']}/{analysis['total_keywords']}")
                print(f"   🧠 Capability awareness: {'✅ Good' if analysis['capability_awareness'] else '❌ Poor'}")
                
                if not analysis['capability_awareness']:
                    print(f"   ⚠️  Issues: {analysis['analysis']}")
                    if analysis['has_negative_indicators']:
                        print("   ⚠️  Contains negative capability indicators")
            else:
                print(f"   ❌ Request failed: {result.get('error', 'Unknown error')}")
            
            # Small delay between requests
            await asyncio.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 CAPABILITY AWARENESS SUMMARY")
    print("=" * 60)
    
    successful_tests = [r for r in results if r["success"]]
    aware_responses = [r for r in successful_tests if r["capability_awareness"]]
    
    print(f"Total tests: {len(TEST_CASES)}")
    print(f"Successful requests: {len(successful_tests)}")
    print(f"Capability-aware responses: {len(aware_responses)}")
    print(f"Success rate: {len(successful_tests)/len(TEST_CASES)*100:.1f}%")
    print(f"Capability awareness rate: {len(aware_responses)/len(successful_tests)*100:.1f}%" if successful_tests else "N/A")
    
    # Detailed results
    print(f"\n📝 DETAILED RESULTS:")
    for result in results:
        status = "✅" if result["success"] and result.get("capability_awareness", False) else "❌"
        print(f"{status} {result['test_name']}")
        if result["success"]:
            print(f"    Keywords: {result['keyword_matches']}/{result['total_keywords']}")
            if not result.get("capability_awareness", False):
                print(f"    Issue: {result.get('analysis', 'Unknown issue')}")
    
    return results

if __name__ == "__main__":
    try:
        results = asyncio.run(run_capability_tests())
        
        # Exit with appropriate code
        successful_tests = [r for r in results if r["success"]]
        aware_responses = [r for r in successful_tests if r.get("capability_awareness", False)]
        
        if len(aware_responses) >= len(successful_tests) * 0.8:  # 80% capability awareness threshold
            print("\n🎉 A.T.L.A.S. AI shows good capability awareness!")
            sys.exit(0)
        else:
            print("\n⚠️  A.T.L.A.S. AI needs improvement in capability awareness.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
