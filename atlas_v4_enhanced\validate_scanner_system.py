"""
A.T.L.A.S. Scanner System Validation Script
Quick validation of the real-time scanner system functionality
"""

import asyncio
import logging
import sys
import os
import time
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

from atlas_realtime_scanner import AtlasRealtimeScanner
from atlas_lee_method import LeeMethodScanner
from atlas_market_core import AtlasMarketEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

async def validate_market_engine():
    """Validate market engine functionality"""
    logger.info("🔍 Validating Market Engine...")
    
    try:
        market_engine = AtlasMarketEngine()
        await market_engine.initialize()
        
        # Test getting a quote
        quote = await market_engine.get_quote('AAPL')
        if quote:
            logger.info(f"✅ Market Engine: Successfully retrieved AAPL quote - ${quote.price}")
            return True
        else:
            logger.warning("⚠️ Market Engine: Could not retrieve quote (may be expected in test mode)")
            return True  # Still consider valid if in test mode
            
    except Exception as e:
        logger.error(f"❌ Market Engine validation failed: {e}")
        return False

async def validate_lee_method_scanner():
    """Validate Lee Method scanner functionality"""
    logger.info("🎯 Validating Lee Method Scanner...")
    
    try:
        lee_scanner = LeeMethodScanner()
        await lee_scanner.initialize()
        
        # Test pattern detection with a symbol
        signal = await lee_scanner.scan_symbol('AAPL')
        if signal:
            logger.info(f"✅ Lee Method Scanner: Pattern detected for AAPL - Confidence: {signal.confidence:.2f}")
        else:
            logger.info("✅ Lee Method Scanner: No pattern detected (normal operation)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Lee Method Scanner validation failed: {e}")
        return False

async def validate_realtime_scanner():
    """Validate real-time scanner functionality"""
    logger.info("🔄 Validating Real-Time Scanner...")
    
    try:
        scanner = AtlasRealtimeScanner()
        
        # Test initialization
        result = await scanner.initialize()
        if not result:
            logger.error("❌ Real-Time Scanner initialization failed")
            return False
        
        logger.info("✅ Real-Time Scanner: Initialization successful")
        
        # Test configuration
        config_update = {'scan_interval': 60, 'min_confidence': 0.7}
        result = await scanner.update_config(config_update)
        if result:
            logger.info("✅ Real-Time Scanner: Configuration update successful")
        else:
            logger.error("❌ Real-Time Scanner: Configuration update failed")
            return False
        
        # Test getting status
        status = scanner.get_scanner_status()
        logger.info(f"✅ Real-Time Scanner: Status retrieved - Running: {status['running']}")
        
        # Test getting active results
        results = await scanner.get_active_results()
        logger.info(f"✅ Real-Time Scanner: Active results retrieved - Count: {len(results)}")
        
        # Test starting scanner (briefly)
        logger.info("🚀 Testing scanner startup...")
        start_result = await scanner.start_scanner()
        if start_result:
            logger.info("✅ Real-Time Scanner: Started successfully")
            
            # Let it run for a few seconds
            await asyncio.sleep(3)
            
            # Stop the scanner
            stop_result = await scanner.stop_scanner()
            if stop_result:
                logger.info("✅ Real-Time Scanner: Stopped successfully")
            else:
                logger.error("❌ Real-Time Scanner: Failed to stop")
                return False
        else:
            logger.error("❌ Real-Time Scanner: Failed to start")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Real-Time Scanner validation failed: {e}")
        return False

async def validate_api_endpoints():
    """Validate API endpoints (simulated)"""
    logger.info("🌐 Validating API Endpoints...")
    
    try:
        # Import the server module to check endpoints exist
        import atlas_server
        
        # Check if the required endpoints exist
        app = atlas_server.app
        routes = [route.path for route in app.routes]
        
        required_endpoints = [
            '/ws/scanner',
            '/api/v1/scanner/status',
            '/api/v1/scanner/results',
            '/api/v1/scanner/config',
            '/api/v1/scanner/start',
            '/api/v1/scanner/stop'
        ]
        
        missing_endpoints = []
        for endpoint in required_endpoints:
            if endpoint not in routes:
                missing_endpoints.append(endpoint)
        
        if missing_endpoints:
            logger.error(f"❌ Missing API endpoints: {missing_endpoints}")
            return False
        else:
            logger.info("✅ All required API endpoints are present")
            return True
            
    except Exception as e:
        logger.error(f"❌ API endpoint validation failed: {e}")
        return False

async def validate_web_interface():
    """Validate web interface components"""
    logger.info("🖥️ Validating Web Interface...")
    
    try:
        # Check if the interface file exists and contains scanner components
        interface_file = os.path.join(os.path.dirname(__file__), 'atlas_interface.html')
        
        if not os.path.exists(interface_file):
            logger.error("❌ Web interface file not found")
            return False
        
        with open(interface_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required scanner components
        required_components = [
            'scanner-sidebar',
            'scannerResults',
            'connectScannerWebSocket',
            'updateScannerDisplay',
            'toggleScanner',
            'showScannerConfig'
        ]
        
        missing_components = []
        for component in required_components:
            if component not in content:
                missing_components.append(component)
        
        if missing_components:
            logger.error(f"❌ Missing web interface components: {missing_components}")
            return False
        else:
            logger.info("✅ All required web interface components are present")
            return True
            
    except Exception as e:
        logger.error(f"❌ Web interface validation failed: {e}")
        return False

async def run_system_validation():
    """Run complete system validation"""
    logger.info("🚀 Starting A.T.L.A.S. Scanner System Validation")
    logger.info("=" * 60)
    
    validation_results = {}
    
    # Run all validations
    validations = [
        ("Market Engine", validate_market_engine),
        ("Lee Method Scanner", validate_lee_method_scanner),
        ("Real-Time Scanner", validate_realtime_scanner),
        ("API Endpoints", validate_api_endpoints),
        ("Web Interface", validate_web_interface)
    ]
    
    for name, validation_func in validations:
        logger.info(f"\n📋 Running {name} validation...")
        try:
            result = await validation_func()
            validation_results[name] = result
            if result:
                logger.info(f"✅ {name} validation: PASSED")
            else:
                logger.error(f"❌ {name} validation: FAILED")
        except Exception as e:
            logger.error(f"❌ {name} validation: ERROR - {e}")
            validation_results[name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 VALIDATION SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in validation_results.values() if result)
    total = len(validation_results)
    
    for name, result in validation_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{name:<20}: {status}")
    
    logger.info("-" * 60)
    logger.info(f"Overall Result: {passed}/{total} validations passed")
    
    if passed == total:
        logger.info("🎉 ALL VALIDATIONS PASSED - Scanner system is ready!")
        return True
    else:
        logger.error(f"⚠️ {total - passed} validation(s) failed - Please review and fix issues")
        return False

def print_system_info():
    """Print system information"""
    logger.info("📋 System Information:")
    logger.info(f"Python Version: {sys.version}")
    logger.info(f"Working Directory: {os.getcwd()}")
    logger.info(f"Validation Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check for required dependencies
    required_modules = [
        'pandas', 'numpy', 'aiohttp', 'fastapi', 'uvicorn', 'websockets'
    ]
    
    logger.info("\n📦 Dependency Check:")
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✅ {module}: Available")
        except ImportError:
            logger.error(f"❌ {module}: Missing")

if __name__ == '__main__':
    print_system_info()
    
    # Run the validation
    success = asyncio.run(run_system_validation())
    
    if success:
        logger.info("\n🚀 Scanner system validation completed successfully!")
        logger.info("You can now start the A.T.L.A.S. server with: python atlas_server.py")
        sys.exit(0)
    else:
        logger.error("\n❌ Scanner system validation failed!")
        logger.error("Please fix the issues before starting the system.")
        sys.exit(1)
