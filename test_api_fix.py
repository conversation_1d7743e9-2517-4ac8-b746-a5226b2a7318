#!/usr/bin/env python3
"""
Test script to verify FMP API key fix
"""

import asyncio
import sys
import os

# Add helper tools to path
sys.path.append('4_helper_tools')
from lee_method_scanner import LeeMethodScanner

async def test_api_fix():
    """Test that the FMP API key is working correctly"""
    print("🔧 Testing FMP API Key Fix")
    print("=" * 50)
    
    # Create scanner instance
    scanner = LeeMethodScanner()
    print(f"Scanner API Key: {scanner.fmp_api_key}")
    
    # Test data fetch
    print("\n📊 Testing data fetch for AAPL...")
    try:
        df = await scanner.fetch_historical_data('AAPL', '1day', 10)
        
        if not df.empty:
            print(f"✅ SUCCESS! Retrieved {len(df)} rows of data")
            print(f"📈 Latest AAPL price: ${df.iloc[-1]['close']:.2f}")
            print("🎉 FMP API key is working correctly!")
            return True
        else:
            print("❌ FAILED: No data retrieved")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_api_fix())
    if success:
        print("\n🚀 API fix successful - Lee Method scanner should now work!")
    else:
        print("\n⚠️ API fix failed - check configuration")
