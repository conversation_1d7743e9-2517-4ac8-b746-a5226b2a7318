{"timestamp": "2025-07-17T10:31:45.504973", "duration": 53.36345171928406, "summary": {"total_tests": 18, "passed": 4, "failed": 14}, "results": [{"name": "Database Connectivity", "category": "Backend", "status": "PASSED", "message": "All 6 databases connected successfully", "details": {"atlas.db": "Connected", "atlas_memory.db": "Connected", "atlas_rag.db": "Connected", "atlas_compliance.db": "Connected", "atlas_feedback.db": "Connected", "atlas_enhanced_memory.db": "Connected"}, "errors": [], "warnings": [], "performance": {"duration": 0.0001709461212158203}}, {"name": "Configuration Loading", "category": "Backend", "status": "PASSED", "message": "Configuration loaded successfully", "details": {"API Keys": {"OpenAI": true, "Alpaca": true, "FMP": true, "Predicto": true}, "Environment": "development", "Port": 8080}, "errors": [], "warnings": [], "performance": {"duration": 1.1205673217773438e-05}}, {"name": "Core Engine Initialization", "category": "Backend", "status": "PASSED", "message": "All engines initialized successfully", "details": {"Engines": {"database": "active", "utils": "active", "market": "active", "risk": "active", "trading": "active", "education": "active", "ai": "active", "lee_method": "initialized"}, "Active Engines": "8/8"}, "errors": [], "warnings": [], "performance": {"duration": 2.3096420764923096}}, {"name": "AI Core Functionality", "category": "Backend", "status": "FAILED", "message": "No AI response generated", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 0.0001785755157470703}}, {"name": "Market Data Access", "category": "Backend", "status": "FAILED", "message": "Unable to fetch market data", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 0.29744553565979004}}, {"name": "<PERSON>", "category": "Backend", "status": "PASSED", "message": "Scanner operational (no patterns in test data)", "details": {"Pattern Detection": "Functional", "Scanner Type": "<PERSON> (5-point TTM Squeeze)"}, "errors": [], "warnings": [], "performance": {"duration": 0.014882564544677734}}, {"name": "Health Check API", "category": "Integration", "status": "FAILED", "message": "Server not running on port 8080", "details": {}, "errors": ["Please start the server: python atlas_server.py"], "warnings": [], "performance": {"duration": 4.047852039337158}}, {"name": "Chat API", "category": "Integration", "status": "FAILED", "message": "Chat API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A881E350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A881E350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.021574258804321}}, {"name": "Market Quote API", "category": "Integration", "status": "FAILED", "message": "Market API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/quote/AAPL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A881F4D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/quote/AAPL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A881F4D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.031005859375}}, {"name": "Scanner Status API", "category": "Integration", "status": "FAILED", "message": "Scanner API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A88F9480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A88F9480>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.029376983642578}}, {"name": "Portfolio API", "category": "Integration", "status": "FAILED", "message": "Portfolio API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/portfolio (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A88F96E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/portfolio (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A88F96E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.034114360809326}}, {"name": "Risk Assessment API", "category": "Integration", "status": "FAILED", "message": "Risk API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/risk-assessment (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A889B410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/risk-assessment (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A889B410>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.052598237991333}}, {"name": "Web Interface Loading", "category": "Frontend", "status": "FAILED", "message": "Web interface error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A891C7C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A891C7C0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.076406478881836}}, {"name": "WebSocket Scanner Connection", "category": "Frontend", "status": "FAILED", "message": "WebSocket connection failed", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 2.001251459121704}}, {"name": "Static Assets Loading", "category": "Frontend", "status": "FAILED", "message": "Some static assets not accessible", "details": {"/static/atlas_interface.html": "✗ Failed", "/static/requirements.txt": "✗ Failed"}, "errors": ["WebSocket error: [WinError 10061] No connection could be made because the target machine actively refused it"], "warnings": [], "performance": {"duration": 8.192308187484741}}, {"name": "Trading Analysis Workflow", "category": "End-to-End", "status": "FAILED", "message": "Workflow error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A8921B50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A8921B50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.080097436904907}}, {"name": "Scanner to Signal Display", "category": "End-to-End", "status": "FAILED", "message": "Scanner workflow error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A89407D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A89407D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.08029842376709}}, {"name": "Educational Query Workflow", "category": "End-to-End", "status": "FAILED", "message": "Educational workflow error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A8940E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002D1A8940E60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.081812620162964}}]}