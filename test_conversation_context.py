#!/usr/bin/env python3
"""
Test script to verify A.T.L.A.S. AI conversation context management
Tests the specific issue where follow-up messages lose context
"""

import asyncio
import aiohttp
import json
import sys
from typing import Dict, Any

async def test_conversation_context():
    """Test conversation context with the specific scenario mentioned"""
    print("🧪 Testing A.T.L.A.S. AI Conversation Context Management")
    print("Testing the specific scenario: '$20 by market close' -> '$30,000 in my account, risk would be 5'")
    print("=" * 80)
    
    session_id = f"context_test_{int(asyncio.get_event_loop().time())}"
    
    async with aiohttp.ClientSession() as session:
        # First message: Goal-based trading request
        print("\n1. First Message: Goal-based trading request")
        first_message = "I need to make $20 by market close today"
        print(f"   Sending: '{first_message}'")
        
        payload1 = {
            "message": first_message,
            "session_id": session_id
        }
        
        async with session.post("http://localhost:8080/api/v1/chat", json=payload1) as response:
            if response.status == 200:
                data1 = await response.json()
                print(f"   ✅ Response received (confidence: {data1.get('confidence', 0):.2f})")
                print(f"   💬 AI Response: {data1.get('response', '')[:200]}...")
                
                # Check if AI asks for capital/risk info
                response_text = data1.get('response', '').lower()
                asks_for_capital = any(word in response_text for word in ['capital', 'account', 'money', 'funds', 'risk'])
                print(f"   📊 Asks for capital/risk info: {'✅ Yes' if asks_for_capital else '❌ No'}")
            else:
                print(f"   ❌ First request failed: HTTP {response.status}")
                return False
        
        # Wait a moment
        await asyncio.sleep(2)
        
        # Second message: Follow-up with capital and risk info
        print("\n2. Second Message: Follow-up with capital and risk tolerance")
        second_message = "$30,000 in my account, risk would be 5"
        print(f"   Sending: '{second_message}'")
        
        payload2 = {
            "message": second_message,
            "session_id": session_id  # Same session ID to maintain context
        }
        
        async with session.post("http://localhost:8080/api/v1/chat", json=payload2) as response:
            if response.status == 200:
                data2 = await response.json()
                print(f"   ✅ Response received (confidence: {data2.get('confidence', 0):.2f})")
                response_text = data2.get('response', '')
                print(f"   💬 AI Response: {response_text[:300]}...")
                
                # Check if AI treats "I" as a stock symbol (the bug)
                treats_i_as_symbol = any(phrase in response_text.lower() for phrase in [
                    'symbol i', 'stock i', 'ticker i', 'analyzing i', 'i stock', 'i symbol'
                ])
                
                # Check if AI understands this is follow-up context
                understands_context = any(phrase in response_text.lower() for phrase in [
                    '$30,000', '30000', 'account', 'capital', 'risk', 'trading plan', 'strategy'
                ])
                
                # Check if AI provides a trading plan
                provides_plan = any(phrase in response_text.lower() for phrase in [
                    'plan', 'strategy', 'recommend', 'suggest', 'trade', 'position', 'shares'
                ])
                
                print(f"   🐛 Treats 'I' as stock symbol: {'❌ Yes (BUG!)' if treats_i_as_symbol else '✅ No'}")
                print(f"   🧠 Understands context: {'✅ Yes' if understands_context else '❌ No'}")
                print(f"   📋 Provides trading plan: {'✅ Yes' if provides_plan else '❌ No'}")
                
                # Overall assessment
                context_working = understands_context and not treats_i_as_symbol and provides_plan
                print(f"   🎯 Context management: {'✅ Working' if context_working else '❌ Broken'}")
                
                return context_working
                
            else:
                print(f"   ❌ Second request failed: HTTP {response.status}")
                return False

async def test_multiple_context_scenarios():
    """Test multiple conversation context scenarios"""
    print("\n" + "=" * 80)
    print("🔄 Testing Multiple Context Scenarios")
    print("=" * 80)
    
    scenarios = [
        {
            "name": "Goal-based Trading Context",
            "messages": [
                "I want to make $100 today",
                "I have $50,000 and moderate risk tolerance"
            ],
            "expected_context": ["$50,000", "moderate risk", "trading plan"]
        },
        {
            "name": "Stock Analysis Context", 
            "messages": [
                "What do you think about AAPL?",
                "Should I buy it now or wait?"
            ],
            "expected_context": ["aapl", "buy", "timing", "analysis"]
        },
        {
            "name": "Lee Method Context",
            "messages": [
                "Can you scan for Lee Method patterns?",
                "Focus on TSLA and NVDA"
            ],
            "expected_context": ["lee method", "tsla", "nvda", "pattern"]
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. Testing: {scenario['name']}")
        session_id = f"scenario_{i}_{int(asyncio.get_event_loop().time())}"
        
        async with aiohttp.ClientSession() as session:
            context_maintained = True
            
            for j, message in enumerate(scenario['messages']):
                print(f"   Message {j+1}: '{message}'")
                
                payload = {
                    "message": message,
                    "session_id": session_id
                }
                
                async with session.post("http://localhost:8080/api/v1/chat", json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        response_text = data.get('response', '').lower()
                        
                        if j == 1:  # Check context on second message
                            context_keywords = scenario['expected_context']
                            context_matches = sum(1 for keyword in context_keywords if keyword.lower() in response_text)
                            context_score = context_matches / len(context_keywords)
                            
                            print(f"   Context score: {context_score:.2f} ({context_matches}/{len(context_keywords)} keywords)")
                            if context_score < 0.5:
                                context_maintained = False
                    else:
                        print(f"   ❌ Request failed: HTTP {response.status}")
                        context_maintained = False
                
                await asyncio.sleep(1)
            
            results.append({
                "scenario": scenario['name'],
                "context_maintained": context_maintained
            })
            print(f"   Result: {'✅ Context maintained' if context_maintained else '❌ Context lost'}")
    
    # Summary
    print(f"\n📊 CONTEXT TEST SUMMARY:")
    successful_scenarios = sum(1 for r in results if r['context_maintained'])
    print(f"Successful scenarios: {successful_scenarios}/{len(scenarios)}")
    print(f"Context success rate: {successful_scenarios/len(scenarios)*100:.1f}%")
    
    return successful_scenarios >= len(scenarios) * 0.8  # 80% success threshold

async def main():
    """Run all conversation context tests"""
    try:
        print("🚀 Starting A.T.L.A.S. Conversation Context Tests")
        
        # Test the specific reported issue
        specific_issue_fixed = await test_conversation_context()
        
        # Test multiple scenarios
        general_context_working = await test_multiple_context_scenarios()
        
        # Final assessment
        print("\n" + "=" * 80)
        print("🏁 FINAL ASSESSMENT")
        print("=" * 80)
        print(f"Specific issue (I symbol bug): {'✅ Fixed' if specific_issue_fixed else '❌ Still broken'}")
        print(f"General context management: {'✅ Working' if general_context_working else '❌ Needs work'}")
        
        if specific_issue_fixed and general_context_working:
            print("\n🎉 Conversation context management is working properly!")
            return True
        else:
            print("\n⚠️  Conversation context management needs further fixes.")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        sys.exit(1)
