#!/usr/bin/env python3
"""
Test script for the new 5-point TTM Squeeze implementation (branded as Lee Method)
Validates that the pattern detection works correctly with the new algorithm
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_lee_method import LeeMethodScanner, get_lee_method_criteria

def create_test_data():
    """Create test data with a TTM Squeeze rebound pattern"""
    dates = pd.date_range(start='2024-01-01', periods=50, freq='D')
    
    # Create price data that will generate the desired pattern
    np.random.seed(42)  # For reproducible results
    
    # Base price trend
    base_prices = np.linspace(100, 110, 50)
    noise = np.random.normal(0, 1, 50)
    close_prices = base_prices + noise
    
    # Create high/low based on close
    high_prices = close_prices + np.random.uniform(0.5, 2.0, 50)
    low_prices = close_prices - np.random.uniform(0.5, 2.0, 50)
    
    # Create volume
    volume = np.random.randint(1000000, 5000000, 50)
    
    df = pd.DataFrame({
        'date': dates,
        'open': close_prices + np.random.uniform(-0.5, 0.5, 50),
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volume
    })
    
    return df

async def test_ttm_squeeze_pattern_detection():
    """Test the 5-point TTM Squeeze pattern detection"""
    print("🧪 Testing 5-Point TTM Squeeze Pattern Detection (Lee Method)")
    print("=" * 60)
    
    # Initialize scanner
    scanner = LeeMethodScanner()
    
    # Test configuration
    print("📋 Testing Configuration:")
    config = scanner.get_ttm_squeeze_config()
    print(f"   MACD Settings: ({config['macd_fast']}, {config['macd_slow']}, {config['macd_signal']})")
    print(f"   BB Period: {config['bb_period']}, Std: {config['bb_std']}")
    print(f"   KC Period: {config['kc_period']}, Multiplier: {config['kc_multiplier']}")
    print(f"   EMA Periods: {config['ema5_period']}, {config['ema8_period']}")
    print(f"   Require Squeeze: {config['require_squeeze']}")
    print()
    
    # Test squeeze filter configuration
    print("⚙️  Testing Squeeze Filter Configuration:")
    scanner.configure_squeeze_filter(require_squeeze=True, squeeze_lookback=0)
    print("   ✓ Configured to require active squeeze")
    
    scanner.configure_squeeze_filter(require_squeeze=False, squeeze_lookback=0)
    print("   ✓ Configured to not require squeeze")
    print()
    
    # Test indicator calculations
    print("📊 Testing TTM Squeeze Indicator Calculations:")
    test_data = create_test_data()
    df_with_indicators = scanner.calculate_lee_method_indicators(test_data)
    
    required_columns = ['macd', 'signal', 'histogram', 'ema5', 'ema8', 
                       'bb_upper', 'bb_lower', 'kc_upper', 'kc_lower', 'squeeze_active']
    
    for col in required_columns:
        if col in df_with_indicators.columns:
            print(f"   ✓ {col} calculated successfully")
        else:
            print(f"   ❌ {col} missing")
    print()
    
    # Test pattern detection functions
    print("🔍 Testing Pattern Detection Functions:")
    
    # Test histogram decline pattern
    decline_result = scanner._check_histogram_decline_pattern(df_with_indicators)
    print(f"   Histogram Decline Pattern: {decline_result['pattern_found']}")
    
    # Test histogram rebound signal
    rebound_result = scanner._check_histogram_rebound_signal(df_with_indicators)
    print(f"   Histogram Rebound Signal: {rebound_result['rebound_found']}")
    
    # Test EMA uptrends
    ema5_uptrend = scanner._check_ema5_uptrend(df_with_indicators)
    ema8_uptrend = scanner._check_ema8_uptrend(df_with_indicators)
    print(f"   EMA 5 Uptrend: {ema5_uptrend}")
    print(f"   EMA 8 Uptrend: {ema8_uptrend}")
    
    # Test squeeze filter
    squeeze_ok = scanner._check_squeeze_filter(df_with_indicators)
    print(f"   Squeeze Filter: {squeeze_ok}")
    print()
    
    # Test full pattern detection
    print("🎯 Testing Full Pattern Detection:")
    pattern_result = scanner.detect_lee_method_pattern(df_with_indicators)
    
    if pattern_result:
        print("   ✓ Pattern detection completed successfully")
        print(f"   Pattern Found: {pattern_result['pattern_found']}")
        print(f"   Signal Direction: {pattern_result['signal_direction']}")
        print(f"   Confidence: {pattern_result['confidence']:.2f}")
    else:
        print("   ❌ Pattern detection failed")
    print()
    
    return True

def test_criteria_information():
    """Test the criteria information function"""
    print("📖 Testing Lee Method Criteria Information:")
    criteria = get_lee_method_criteria()
    
    print(f"   Name: {criteria['name']}")
    print(f"   Algorithm: {criteria['algorithm']}")
    print(f"   Description: {criteria['description']}")
    print(f"   Signal Type: {criteria['signal_type']}")
    print(f"   Number of Criteria: {len(criteria['criteria'])}")
    
    for i, criterion in enumerate(criteria['criteria'], 1):
        print(f"   {i}. {criterion['name']}: {criterion['description']}")
    
    print(f"   Advantages: {len(criteria['advantages'])} listed")
    print()

async def main():
    """Main test function"""
    print("🚀 A.T.L.A.S. Lee Method - 5-Point TTM Squeeze Implementation Test")
    print("=" * 70)
    print()
    
    try:
        # Test criteria information
        test_criteria_information()
        
        # Test pattern detection
        await test_ttm_squeeze_pattern_detection()
        
        print("✅ All tests completed successfully!")
        print()
        print("🎉 The 5-point TTM Squeeze implementation (Lee Method) is ready!")
        print("   - TTM Squeeze histogram calculations: ✓")
        print("   - 5-point pattern detection: ✓")
        print("   - EMA trend confirmations: ✓")
        print("   - Optional squeeze filter: ✓")
        print("   - Lee Method branding maintained: ✓")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
