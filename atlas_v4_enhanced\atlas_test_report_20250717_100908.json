{"timestamp": "2025-07-17T10:09:08.030599", "duration": 52.01793956756592, "summary": {"total_tests": 18, "passed": 3, "failed": 15}, "results": [{"name": "Database Connectivity", "category": "Backend", "status": "PASSED", "message": "All 6 databases connected successfully", "details": {"atlas.db": "Connected", "atlas_memory.db": "Connected", "atlas_rag.db": "Connected", "atlas_compliance.db": "Connected", "atlas_feedback.db": "Connected", "atlas_enhanced_memory.db": "Connected"}, "errors": [], "warnings": [], "performance": {"duration": 0.0003180503845214844}}, {"name": "Configuration Loading", "category": "Backend", "status": "PASSED", "message": "Configuration loaded successfully", "details": {"API Keys": {"OpenAI": true, "Alpaca": true, "FMP": true, "Predicto": true}, "Environment": "development", "Port": 8080}, "errors": [], "warnings": [], "performance": {"duration": 7.867813110351562e-06}}, {"name": "Core Engine Initialization", "category": "Backend", "status": "PASSED", "message": "All engines initialized successfully", "details": {"Engines": {"database": "active", "utils": "active", "market": "active", "risk": "active", "trading": "active", "education": "active", "ai": "active", "lee_method": "initialized"}, "Active Engines": "8/8"}, "errors": [], "warnings": [], "performance": {"duration": 1.085235834121704}}, {"name": "AI Core Functionality", "category": "Backend", "status": "FAILED", "message": "No AI response generated", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 0.000148773193359375}}, {"name": "Market Data Access", "category": "Backend", "status": "FAILED", "message": "Unable to fetch market data", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 0.23094797134399414}}, {"name": "<PERSON>", "category": "Backend", "status": "FAILED", "message": "Scanner error: 'LeeMethodScanner' object has no attribute 'detect_pattern'", "details": {}, "errors": ["'LeeMethodScanner' object has no attribute 'detect_pattern'"], "warnings": [], "performance": {"duration": 0.0012967586517333984}}, {"name": "Health Check API", "category": "Integration", "status": "FAILED", "message": "Server not running on port 8080", "details": {}, "errors": ["Please start the server: python atlas_server.py"], "warnings": [], "performance": {"duration": 4.079230070114136}}, {"name": "Chat API", "category": "Integration", "status": "FAILED", "message": "Chat API error: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1183EDA90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1183EDA90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.06530499458313}}, {"name": "Market Quote API", "category": "Integration", "status": "FAILED", "message": "Market API error: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/quote/AAPL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1183EEC10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/quote/AAPL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1183EEC10>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.0481178760528564}}, {"name": "Scanner Status API", "category": "Integration", "status": "FAILED", "message": "Scanner API error: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184B50F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184B50F0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.06597900390625}}, {"name": "Portfolio API", "category": "Integration", "status": "FAILED", "message": "Portfolio API error: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/portfolio (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184B5350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/portfolio (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184B5350>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.048494815826416}}, {"name": "Risk Assessment API", "category": "Integration", "status": "FAILED", "message": "Risk API error: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/risk-assessment (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184530B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/risk-assessment (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184530B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.047466039657593}}, {"name": "Web Interface Loading", "category": "Frontend", "status": "FAILED", "message": "Web interface error: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1183F7BD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1183F7BD0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.051622152328491}}, {"name": "WebSocket Scanner Connection", "category": "Frontend", "status": "FAILED", "message": "WebSocket connection failed", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 2.001058340072632}}, {"name": "Static Assets Loading", "category": "Frontend", "status": "FAILED", "message": "Some static assets not accessible", "details": {"/static/atlas_interface.html": "✗ Failed", "/static/requirements.txt": "✗ Failed"}, "errors": ["WebSocket error: [WinError 10061] No connection could be made because the target machine actively refused it"], "warnings": [], "performance": {"duration": 8.112031936645508}}, {"name": "Trading Analysis Workflow", "category": "End-to-End", "status": "FAILED", "message": "Workflow error: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C118475B50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C118475B50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.0671491622924805}}, {"name": "Scanner to Signal Display", "category": "End-to-End", "status": "FAILED", "message": "Scanner workflow error: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184BE210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184BE210>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.04660964012146}}, {"name": "Educational Query Workflow", "category": "End-to-End", "status": "FAILED", "message": "Educational workflow error: HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184BE8A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002C1184BE8A0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.04782772064209}}]}