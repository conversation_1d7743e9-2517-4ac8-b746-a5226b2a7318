<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. Trading System - Advanced Trading & Learning Analysis System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2d3748 100%);
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px 20px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .header-left h1 {
            font-size: 2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            color: #4CAF50;
        }

        .header-left p {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
        
        .header-right {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9em;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }

        .sidebar {
            width: 280px;
            background: rgba(0, 0, 0, 0.4);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            overflow-y: auto;
        }

        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .scanner-sidebar {
            width: 350px;
            background: rgba(0, 0, 0, 0.4);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .scanner-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .scanner-title {
            font-size: 1.2em;
            color: #4CAF50;
            font-weight: bold;
        }

        .scanner-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
        }

        .scanner-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        .scanner-status-dot.inactive {
            background: #f44336;
            animation: none;
        }

        .scanner-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .scanner-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
            flex: 1;
        }

        .scanner-btn.primary {
            background: #4CAF50;
            color: white;
        }

        .scanner-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .scanner-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }

        .scanner-results {
            flex: 1;
            overflow-y: auto;
        }

        .scanner-result-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .scanner-result-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: #4CAF50;
            transform: translateY(-2px);
        }

        .scanner-result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .scanner-symbol {
            font-size: 1.1em;
            font-weight: bold;
            color: #4CAF50;
        }

        .scanner-confidence {
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 12px;
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }

        .scanner-price {
            font-size: 1em;
            margin-bottom: 5px;
        }

        .scanner-change {
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .scanner-change.positive {
            color: #4CAF50;
        }

        .scanner-change.negative {
            color: #f44336;
        }

        .scanner-indicators {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .scanner-indicator {
            font-size: 0.7em;
            padding: 2px 6px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .scanner-indicator.active {
            background: rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .scanner-timestamp {
            font-size: 0.7em;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 8px;
        }

        .scanner-empty {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            padding: 40px 20px;
            font-style: italic;
        }

        .nav-section {
            margin-bottom: 25px;
        }

        .nav-title {
            font-size: 1.1em;
            color: #4CAF50;
            margin-bottom: 10px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-item {
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-item:hover {
            background: rgba(76, 175, 80, 0.2);
            border-color: rgba(76, 175, 80, 0.3);
            transform: translateX(5px);
        }

        .nav-item.active {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        .nav-icon {
            font-size: 1.1em;
        }

        .tab-content {
            display: none;
            padding: 20px;
            height: 100%;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        .panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }

        .panel-title {
            font-size: 1.4em;
            color: #4CAF50;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .grid-3 {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .grid-4 {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .metric-card {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #4CAF50;
        }

        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            text-transform: uppercase;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .btn-danger {
            background: #f44336;
            color: white;
        }

        .btn-danger:hover {
            background: #da190b;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #4CAF50;
        }

        .input-field {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9em;
        }

        .input-field:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        .select-field {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 0.9em;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th,
        .table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table th {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            font-weight: bold;
        }

        .positive {
            color: #4CAF50;
        }

        .negative {
            color: #f44336;
        }

        .chat-container {
            height: 400px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 10px;
            max-width: 85%;
            word-wrap: break-word;
        }

        .user-message {
            background: rgba(74, 144, 226, 0.8);
            margin-left: auto;
            text-align: right;
        }

        .bot-message {
            background: rgba(46, 204, 113, 0.8);
            margin-right: auto;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
        }

        .send-btn {
            padding: 15px 25px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .send-btn:hover {
            background: #45a049;
        }

        .send-btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        
        .loading {
            text-align: center;
            color: #ccc;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <h1>A.T.L.A.S. Trading System</h1>
            <p>Advanced Trading & Learning Analysis System</p>
        </div>
        <div class="header-right">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>System Active</span>
            </div>
        </div>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <div class="nav-section">
                <div class="nav-title">🏠 Dashboard</div>
                <div class="nav-item active" data-tab="dashboard">
                    <span class="nav-icon">📊</span>
                    <span>Overview</span>
                </div>
                <div class="nav-item" data-tab="portfolio">
                    <span class="nav-icon">💼</span>
                    <span>Portfolio</span>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-title">📈 Trading</div>
                <div class="nav-item" data-tab="live-trading">
                    <span class="nav-icon">⚡</span>
                    <span>Live Trading</span>
                </div>
                <div class="nav-item" data-tab="paper-trading">
                    <span class="nav-icon">📝</span>
                    <span>Paper Trading</span>
                </div>
                <div class="nav-item" data-tab="auto-trading">
                    <span class="nav-icon">🤖</span>
                    <span>Auto Trading</span>
                </div>
                <div class="nav-item" data-tab="options-trading">
                    <span class="nav-icon">🎯</span>
                    <span>Options Trading</span>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-title">🔍 Analysis</div>
                <div class="nav-item" data-tab="lee-method">
                    <span class="nav-icon">🎯</span>
                    <span>Lee Method Scanner</span>
                </div>
                <div class="nav-item" data-tab="technical-analysis">
                    <span class="nav-icon">📊</span>
                    <span>Technical Analysis</span>
                </div>
                <div class="nav-item" data-tab="sentiment-analysis">
                    <span class="nav-icon">💭</span>
                    <span>Sentiment Analysis</span>
                </div>
                <div class="nav-item" data-tab="ml-predictions">
                    <span class="nav-icon">🧠</span>
                    <span>ML Predictions</span>
                </div>
                <div class="nav-item" data-tab="market-scanner">
                    <span class="nav-icon">🔍</span>
                    <span>Market Scanner</span>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-title">⚠️ Risk Management</div>
                <div class="nav-item" data-tab="risk-assessment">
                    <span class="nav-icon">🛡️</span>
                    <span>Risk Assessment</span>
                </div>
                <div class="nav-item" data-tab="portfolio-optimization">
                    <span class="nav-icon">⚖️</span>
                    <span>Portfolio Optimization</span>
                </div>
                <div class="nav-item" data-tab="compliance">
                    <span class="nav-icon">📋</span>
                    <span>Compliance Monitor</span>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-title">🎓 Education</div>
                <div class="nav-item" data-tab="education">
                    <span class="nav-icon">📚</span>
                    <span>Trading Education</span>
                </div>
                <div class="nav-item" data-tab="strategies">
                    <span class="nav-icon">🎯</span>
                    <span>Strategy Library</span>
                </div>
                <div class="nav-item" data-tab="tutorials">
                    <span class="nav-icon">🎬</span>
                    <span>Video Tutorials</span>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-title">💬 AI Assistant</div>
                <div class="nav-item" data-tab="chat">
                    <span class="nav-icon">🤖</span>
                    <span>Chat Interface</span>
                </div>
                <div class="nav-item" data-tab="performance">
                    <span class="nav-icon">📈</span>
                    <span>Performance Analytics</span>
                </div>
            </div>
        </div>

        <div class="content-area">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="panel-title">📊 A.T.L.A.S. System Dashboard</div>

                <div class="grid-4">
                    <div class="metric-card">
                        <div class="metric-value" id="portfolioValue">$100,000</div>
                        <div class="metric-label">Portfolio Value</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value positive" id="dailyPnL">+$1,250</div>
                        <div class="metric-label">Daily P&L</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="activePositions">5</div>
                        <div class="metric-label">Active Positions</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="leeSignals">3</div>
                        <div class="metric-label">Lee Method Signals</div>
                    </div>
                </div>

                <div class="grid-2">
                    <div class="panel">
                        <div class="panel-title">🔥 Live Market Scanner</div>
                        <div id="liveScanner">
                            <div class="table">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Symbol</th>
                                            <th>Price</th>
                                            <th>Change</th>
                                            <th>Signal</th>
                                        </tr>
                                    </thead>
                                    <tbody id="scannerResults">
                                        <tr><td colspan="4">Loading scanner data...</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="panel">
                        <div class="panel-title">📈 Recent Trades</div>
                        <div id="recentTrades">
                            <div class="table">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Symbol</th>
                                            <th>Action</th>
                                            <th>Quantity</th>
                                            <th>P&L</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tradesTable">
                                        <tr><td colspan="4">No recent trades</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Trading Tab -->
            <div id="live-trading" class="tab-content">
                <div class="panel-title">⚡ Live Trading Interface</div>

                <div class="grid-2">
                    <div class="panel">
                        <div class="panel-title">📊 Order Entry</div>
                        <div class="input-group">
                            <label class="input-label">Symbol</label>
                            <input type="text" class="input-field" id="tradeSymbol" placeholder="e.g., AAPL">
                        </div>
                        <div class="input-group">
                            <label class="input-label">Action</label>
                            <select class="select-field" id="tradeAction">
                                <option value="BUY">Buy</option>
                                <option value="SELL">Sell</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <label class="input-label">Quantity</label>
                            <input type="number" class="input-field" id="tradeQuantity" placeholder="100">
                        </div>
                        <div class="input-group">
                            <label class="input-label">Order Type</label>
                            <select class="select-field" id="orderType">
                                <option value="MARKET">Market</option>
                                <option value="LIMIT">Limit</option>
                                <option value="STOP">Stop</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <label class="input-label">Price (if limit/stop)</label>
                            <input type="number" class="input-field" id="tradePrice" placeholder="0.00" step="0.01">
                        </div>
                        <button class="btn btn-primary" onclick="placeLiveTrade()">Place Live Trade</button>
                    </div>

                    <div class="panel">
                        <div class="panel-title">📋 Live Positions</div>
                        <div id="livePositions">
                            <div class="table">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Symbol</th>
                                            <th>Quantity</th>
                                            <th>Avg Price</th>
                                            <th>Current P&L</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="livePositionsTable">
                                        <tr><td colspan="5">No live positions</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lee Method Scanner Tab -->
            <div id="lee-method" class="tab-content">
                <div class="panel-title">🎯 Lee Method Pattern Scanner</div>

                <div class="panel">
                    <div class="grid-3">
                        <div class="metric-card">
                            <div class="metric-value" id="leeActiveSignals">0</div>
                            <div class="metric-label">Active Signals</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="leeAccuracy">87%</div>
                            <div class="metric-label">Pattern Accuracy</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="leeScansToday">1,247</div>
                            <div class="metric-label">Scans Today</div>
                        </div>
                    </div>

                    <div class="panel-title">⚙️ Scanner Controls</div>
                    <div class="panel" style="margin-bottom: 20px;">
                        <div class="grid-3">
                            <button class="btn btn-primary" onclick="refreshLeeMethodSignals()">🔄 Refresh Scan</button>
                            <button class="btn btn-secondary" onclick="showLeeMethodConfig()">⚙️ Configuration</button>
                            <button class="btn btn-info" onclick="loadLeeMethodCriteria()">📋 View Criteria</button>
                        </div>
                    </div>

                    <div class="panel-title">🔍 Live Lee Method Signals</div>
                    <div id="leeMethodSignals">
                        <div class="table">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Signal Type</th>
                                        <th>Confidence</th>
                                        <th>Entry Price</th>
                                        <th>Target</th>
                                        <th>Stop Loss</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="leeSignalsTable">
                                    <tr><td colspan="7">Scanning for Lee Method patterns...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Configuration Modal (hidden by default) -->
                    <div id="leeConfigModal" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #2d3748; padding: 30px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.5); z-index: 1000; min-width: 400px;">
                        <h3>🎯 Lee Method Configuration</h3>
                        <div style="margin: 20px 0;">
                            <label style="display: block; margin: 10px 0;">
                                <input type="checkbox" id="requireSqueeze" style="margin-right: 10px;">
                                Require Active TTM Squeeze
                            </label>
                            <label style="display: block; margin: 10px 0;">
                                Squeeze Lookback:
                                <input type="number" id="squeezeLookback" value="0" min="0" max="5" style="width: 60px; margin-left: 10px;">
                            </label>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-primary" onclick="saveLeeMethodConfig()">Save</button>
                            <button class="btn btn-secondary" onclick="closeLeeMethodConfig()">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Interface Tab -->
            <div id="chat" class="tab-content">
                <div class="panel-title">🤖 A.T.L.A.S. AI Assistant</div>

                <div class="panel">
                    <div class="chat-container" id="chatMessages">
                        <div class="message bot-message">
                            <strong>A.T.L.A.S. AI:</strong> Welcome to the Advanced Trading & Learning Analysis System! I have access to all 25+ A.T.L.A.S. capabilities including:
                            <br><br>
                            <strong>🔥 Live Trading:</strong> Execute real trades with full risk management
                            <br><strong>🎯 Lee Method:</strong> Advanced pattern detection and scanning
                            <br><strong>🧠 ML Predictions:</strong> LSTM neural network price forecasting
                            <br><strong>💭 Sentiment Analysis:</strong> Multi-source market sentiment
                            <br><strong>📊 Technical Analysis:</strong> 20+ indicators and scanners
                            <br><strong>🎓 Education:</strong> Comprehensive trading tutorials
                            <br><strong>⚖️ Portfolio Optimization:</strong> Risk-adjusted portfolio management
                            <br><strong>🛡️ Risk Management:</strong> Advanced VaR and compliance monitoring
                            <br><br>
                            Ask me to analyze stocks, execute trades, scan for patterns, or provide educational content!
                        </div>
                    </div>
                    <div class="chat-input-container">
                        <input type="text" class="chat-input" id="chatInput" placeholder="Ask A.T.L.A.S. about trading, analysis, education, or any market question..." onkeypress="handleKeyPress(event)">
                        <button class="send-btn" id="sendBtn" onclick="sendMessage()">Send</button>
                    </div>
                </div>
            </div>

            <!-- Portfolio Tab -->
            <div id="portfolio" class="tab-content">
                <div class="panel-title">💼 Portfolio Management</div>

                <div class="grid-2">
                    <div class="panel">
                        <div class="panel-title">📊 Portfolio Overview</div>
                        <div class="grid-2">
                            <div class="metric-card">
                                <div class="metric-value">$125,750</div>
                                <div class="metric-label">Total Value</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value positive">+25.75%</div>
                                <div class="metric-label">Total Return</div>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="optimizePortfolio()">Optimize Portfolio</button>
                    </div>

                    <div class="panel">
                        <div class="panel-title">⚖️ Risk Metrics</div>
                        <div class="grid-2">
                            <div class="metric-card">
                                <div class="metric-value">1.45</div>
                                <div class="metric-label">Sharpe Ratio</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">-$2,150</div>
                                <div class="metric-label">VaR (95%)</div>
                            </div>
                        </div>
                        <button class="btn btn-secondary" onclick="runRiskAssessment()">Risk Assessment</button>
                    </div>
                </div>
            </div>

            <!-- Education Tab -->
            <div id="education" class="tab-content">
                <div class="panel-title">🎓 Trading Education Center</div>

                <div class="grid-3">
                    <div class="panel">
                        <div class="panel-title">📚 Beginner Courses</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 10px 0;"><button class="btn btn-secondary" onclick="loadEducationContent('basics')">Trading Basics</button></li>
                            <li style="margin: 10px 0;"><button class="btn btn-secondary" onclick="loadEducationContent('risk')">Risk Management</button></li>
                            <li style="margin: 10px 0;"><button class="btn btn-secondary" onclick="loadEducationContent('analysis')">Technical Analysis</button></li>
                        </ul>
                    </div>

                    <div class="panel">
                        <div class="panel-title">🎯 Advanced Topics</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 10px 0;"><button class="btn btn-secondary" onclick="loadEducationContent('options')">Options Strategies</button></li>
                            <li style="margin: 10px 0;"><button class="btn btn-secondary" onclick="loadEducationContent('algorithms')">Algorithmic Trading</button></li>
                            <li style="margin: 10px 0;"><button class="btn btn-secondary" onclick="loadEducationContent('portfolio')">Portfolio Theory</button></li>
                        </ul>
                    </div>

                    <div class="panel">
                        <div class="panel-title">🔥 A.T.L.A.S. Specific</div>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 10px 0;"><button class="btn btn-secondary" onclick="loadEducationContent('lee-method')">Lee Method Patterns</button></li>
                            <li style="margin: 10px 0;"><button class="btn btn-secondary" onclick="loadEducationContent('ml-trading')">ML in Trading</button></li>
                            <li style="margin: 10px 0;"><button class="btn btn-secondary" onclick="loadEducationContent('automation')">Trading Automation</button></li>
                        </ul>
                    </div>
                </div>

                <div class="panel">
                    <div class="panel-title">📖 Educational Content</div>
                    <div id="educationContent">
                        <p>Select a topic above to begin learning!</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-Time Scanner Sidebar -->
        <div class="scanner-sidebar">
            <div class="scanner-header">
                <div class="scanner-title">🔍 Live Scanner</div>
                <div class="scanner-status">
                    <div class="scanner-status-dot" id="scannerStatusDot"></div>
                    <span id="scannerStatusText">Scanning...</span>
                </div>
            </div>

            <div class="scanner-controls">
                <button class="scanner-btn primary" onclick="toggleScanner()" id="scannerToggleBtn">
                    ⏸️ Pause
                </button>
                <button class="scanner-btn secondary" onclick="showScannerConfig()">
                    ⚙️ Config
                </button>
            </div>

            <div class="scanner-results" id="scannerResults">
                <div class="scanner-empty">
                    🔍 Scanning S&P 500 for Lee Method patterns...
                </div>
            </div>
        </div>
    </div>

    <!-- Scanner Configuration Modal -->
    <div id="scannerConfigModal" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #2d3748; padding: 30px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.5); z-index: 1000; min-width: 400px;">
        <h3>🔍 Scanner Configuration</h3>
        <div style="margin: 20px 0;">
            <label style="display: block; margin: 10px 0;">
                Scan Interval (seconds):
                <input type="number" id="scanInterval" value="30" min="10" max="300" style="width: 80px; margin-left: 10px; padding: 5px; border-radius: 4px; border: 1px solid #555; background: #1a1a1a; color: white;">
            </label>
            <label style="display: block; margin: 10px 0;">
                <input type="checkbox" id="marketHoursOnly" checked style="margin-right: 10px;">
                Market Hours Only
            </label>
            <label style="display: block; margin: 10px 0;">
                Min Confidence:
                <input type="range" id="minConfidence" min="0.1" max="1.0" step="0.1" value="0.6" style="margin-left: 10px;">
                <span id="confidenceValue">60%</span>
            </label>
            <label style="display: block; margin: 10px 0;">
                <input type="checkbox" id="requireSqueeze" style="margin-right: 10px;">
                Require TTM Squeeze
            </label>
        </div>
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-primary" onclick="saveScannerConfig()">Save</button>
            <button class="btn btn-secondary" onclick="closeScannerConfig()">Cancel</button>
        </div>
    </div>

    <script>
        let sessionId = 'web_' + Date.now();

        // Initialize interface
        initializeInterface();

        function initializeInterface() {
            // Load initial data
            loadDashboardData();
            loadLeeMethodSignals();

            // Set up navigation
            setupNavigation();

            // Load market data
            loadMarketData();

            // Initialize real-time scanner
            initializeRealtimeScanner();
        }

        // ============================================================================
        // REAL-TIME SCANNER FUNCTIONALITY
        // ============================================================================

        let scannerWebSocket = null;
        let scannerRunning = true;
        let scannerResults = [];

        function initializeRealtimeScanner() {
            // Connect to WebSocket for real-time updates
            connectScannerWebSocket();

            // Load initial scanner status
            loadScannerStatus();

            // Set up periodic status updates
            setInterval(loadScannerStatus, 30000); // Every 30 seconds
        }

        function connectScannerWebSocket() {
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/scanner`;

                scannerWebSocket = new WebSocket(wsUrl);

                scannerWebSocket.onopen = function(event) {
                    console.log('Scanner WebSocket connected');
                    updateScannerStatus(true, 'Connected');
                };

                scannerWebSocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'scanner_update') {
                            handleScannerUpdate(data.data);
                        }
                    } catch (error) {
                        console.error('Error parsing WebSocket message:', error);
                    }
                };

                scannerWebSocket.onclose = function(event) {
                    console.log('Scanner WebSocket disconnected');
                    updateScannerStatus(false, 'Disconnected');

                    // Attempt to reconnect after 5 seconds
                    setTimeout(connectScannerWebSocket, 5000);
                };

                scannerWebSocket.onerror = function(error) {
                    console.error('Scanner WebSocket error:', error);
                    updateScannerStatus(false, 'Error');
                };

            } catch (error) {
                console.error('Error connecting to scanner WebSocket:', error);
                updateScannerStatus(false, 'Connection Failed');
            }
        }

        function handleScannerUpdate(result) {
            // Add or update result in the list
            const existingIndex = scannerResults.findIndex(r => r.symbol === result.symbol);

            if (existingIndex >= 0) {
                scannerResults[existingIndex] = result;
            } else {
                scannerResults.unshift(result); // Add to beginning
            }

            // Keep only the most recent 50 results
            if (scannerResults.length > 50) {
                scannerResults = scannerResults.slice(0, 50);
            }

            // Update the display
            updateScannerDisplay();
        }

        function updateScannerDisplay() {
            const container = document.getElementById('scannerResults');

            if (scannerResults.length === 0) {
                container.innerHTML = `
                    <div class="scanner-empty">
                        🔍 Scanning S&P 500 for Lee Method patterns...
                    </div>
                `;
                return;
            }

            // Sort by confidence descending
            const sortedResults = [...scannerResults].sort((a, b) => b.confidence - a.confidence);

            container.innerHTML = sortedResults.map(result => `
                <div class="scanner-result-item" onclick="viewStockAnalysis('${result.symbol}')">
                    <div class="scanner-result-header">
                        <div class="scanner-symbol">${result.symbol}</div>
                        <div class="scanner-confidence">${Math.round(result.confidence * 100)}%</div>
                    </div>

                    <div class="scanner-price">$${result.price.toFixed(2)}</div>
                    <div class="scanner-change ${result.change >= 0 ? 'positive' : 'negative'}">
                        ${result.change >= 0 ? '+' : ''}${result.change.toFixed(2)} (${result.change_percent.toFixed(2)}%)
                    </div>

                    <div class="scanner-indicators">
                        <div class="scanner-indicator ${result.ema5_trend ? 'active' : ''}">EMA5</div>
                        <div class="scanner-indicator ${result.ema8_trend ? 'active' : ''}">EMA8</div>
                        <div class="scanner-indicator ${result.momentum_trend ? 'active' : ''}">MOM</div>
                        <div class="scanner-indicator ${result.squeeze_active ? 'active' : ''}">SQZ</div>
                    </div>

                    <div class="scanner-timestamp">
                        ${new Date(result.timestamp).toLocaleTimeString()}
                    </div>
                </div>
            `).join('');
        }

        function updateScannerStatus(running, statusText) {
            const dot = document.getElementById('scannerStatusDot');
            const text = document.getElementById('scannerStatusText');
            const toggleBtn = document.getElementById('scannerToggleBtn');

            scannerRunning = running;

            if (running) {
                dot.classList.remove('inactive');
                text.textContent = statusText || 'Scanning...';
                toggleBtn.innerHTML = '⏸️ Pause';
            } else {
                dot.classList.add('inactive');
                text.textContent = statusText || 'Stopped';
                toggleBtn.innerHTML = '▶️ Start';
            }
        }

        async function loadScannerStatus() {
            try {
                const response = await fetch('/api/v1/scanner/status');
                const status = await response.json();

                if (status.running !== undefined) {
                    updateScannerStatus(status.running, status.running ? 'Active' : 'Stopped');
                }

            } catch (error) {
                console.error('Error loading scanner status:', error);
            }
        }

        async function toggleScanner() {
            try {
                const endpoint = scannerRunning ? '/api/v1/scanner/stop' : '/api/v1/scanner/start';
                const response = await fetch(endpoint, { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    updateScannerStatus(!scannerRunning, !scannerRunning ? 'Starting...' : 'Stopping...');
                    // Status will be updated by the periodic status check
                } else {
                    alert('Failed to toggle scanner: ' + (result.error || 'Unknown error'));
                }

            } catch (error) {
                console.error('Error toggling scanner:', error);
                alert('Error toggling scanner');
            }
        }

        function showScannerConfig() {
            // Load current config values
            loadScannerConfig();
            document.getElementById('scannerConfigModal').style.display = 'block';
        }

        function closeScannerConfig() {
            document.getElementById('scannerConfigModal').style.display = 'none';
        }

        async function loadScannerConfig() {
            try {
                const response = await fetch('/api/v1/scanner/status');
                const status = await response.json();

                if (status.config) {
                    document.getElementById('scanInterval').value = status.config.scan_interval || 30;
                    document.getElementById('marketHoursOnly').checked = status.config.market_hours_only !== false;
                    document.getElementById('minConfidence').value = status.config.min_confidence || 0.6;
                    document.getElementById('requireSqueeze').checked = status.config.require_squeeze === true;

                    // Update confidence display
                    updateConfidenceDisplay();
                }

            } catch (error) {
                console.error('Error loading scanner config:', error);
            }
        }

        async function saveScannerConfig() {
            try {
                const config = {
                    scan_interval: parseInt(document.getElementById('scanInterval').value),
                    market_hours_only: document.getElementById('marketHoursOnly').checked,
                    min_confidence: parseFloat(document.getElementById('minConfidence').value),
                    require_squeeze: document.getElementById('requireSqueeze').checked
                };

                const response = await fetch('/api/v1/scanner/config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });

                const result = await response.json();

                if (result.success) {
                    alert('Scanner configuration saved successfully!');
                    closeScannerConfig();
                } else {
                    alert('Failed to save configuration: ' + (result.error || 'Unknown error'));
                }

            } catch (error) {
                console.error('Error saving scanner config:', error);
                alert('Error saving configuration');
            }
        }

        function updateConfidenceDisplay() {
            const slider = document.getElementById('minConfidence');
            const display = document.getElementById('confidenceValue');
            display.textContent = Math.round(slider.value * 100) + '%';
        }

        function viewStockAnalysis(symbol) {
            // Switch to chat tab and analyze the stock
            const chatTab = document.querySelector('[data-tab="chat"]');
            const chatContent = document.getElementById('chat');

            if (chatTab && chatContent) {
                // Activate chat tab
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));

                chatTab.classList.add('active');
                chatContent.classList.add('active');

                // Send analysis request
                const message = `Analyze ${symbol} - Lee Method pattern detected. Provide detailed analysis with entry/exit points.`;
                document.getElementById('messageInput').value = message;
                sendMessage();
            }
        }

        // Add event listener for confidence slider
        document.addEventListener('DOMContentLoaded', function() {
            const confidenceSlider = document.getElementById('minConfidence');
            if (confidenceSlider) {
                confidenceSlider.addEventListener('input', updateConfidenceDisplay);
            }
        });

        function setupNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            const tabContents = document.querySelectorAll('.tab-content');

            navItems.forEach(item => {
                item.addEventListener('click', () => {
                    const tabId = item.getAttribute('data-tab');

                    // Remove active class from all nav items and tab contents
                    navItems.forEach(nav => nav.classList.remove('active'));
                    tabContents.forEach(tab => tab.classList.remove('active'));

                    // Add active class to clicked nav item and corresponding tab
                    item.classList.add('active');
                    const targetTab = document.getElementById(tabId);
                    if (targetTab) {
                        targetTab.classList.add('active');
                    }
                });
            });
        }

        function loadDashboardData() {
            // Update dashboard metrics
            updateMetric('portfolioValue', '$125,750');
            updateMetric('dailyPnL', '+$2,150');
            updateMetric('activePositions', '7');
            updateMetric('leeSignals', '4');
        }

        function updateMetric(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message) return;

            // Add user message to chat
            addMessage(message, 'user');
            input.value = '';

            // Disable send button
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.textContent = 'Thinking...';

            try {
                const response = await fetch('/api/v1/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId
                    })
                });

                const data = await response.json();

                if (data.response) {
                    addMessage(data.response, 'bot');
                } else {
                    addMessage('Sorry, I encountered an error processing your request.', 'bot');
                }
            } catch (error) {
                console.error('Error:', error);
                addMessage('Sorry, I\'m having trouble connecting. Please try again.', 'bot');
            }

            // Re-enable send button
            sendBtn.disabled = false;
            sendBtn.textContent = 'Send';
        }
        
        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            if (sender === 'user') {
                messageDiv.innerHTML = `<strong>You:</strong> ${text}`;
            } else {
                messageDiv.innerHTML = `<strong>A.T.L.A.S. AI:</strong> ${formatBotMessage(text)}`;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function formatBotMessage(text) {
            // Convert markdown-style formatting to HTML
            return text
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\n/g, '<br>')
                .replace(/📊|🎯|💡|🚀|📈|📱|🚗|💻|🔥|🧠|💭|🎓|⚖️|🛡️/g, '<span style="font-size: 1.2em;">$&</span>');
        }

        // Trading Functions
        async function placeLiveTrade() {
            const symbol = document.getElementById('tradeSymbol').value;
            const action = document.getElementById('tradeAction').value;
            const quantity = document.getElementById('tradeQuantity').value;
            const orderType = document.getElementById('orderType').value;
            const price = document.getElementById('tradePrice').value;

            if (!symbol || !quantity) {
                alert('Please enter symbol and quantity');
                return;
            }

            try {
                const response = await fetch('/api/v1/trading/place_order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        symbol: symbol.toUpperCase(),
                        side: action,
                        quantity: parseInt(quantity),
                        order_type: orderType,
                        price: price ? parseFloat(price) : null
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`Order placed successfully: ${data.order_id}`);
                    loadLivePositions();
                } else {
                    alert(`Error placing order: ${data.error}`);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error placing trade. Please try again.');
            }
        }

        async function loadLivePositions() {
            try {
                const response = await fetch('/api/v1/trading/positions');
                const data = await response.json();

                const tbody = document.getElementById('livePositionsTable');
                if (data.positions && data.positions.length > 0) {
                    tbody.innerHTML = data.positions.map(pos => `
                        <tr>
                            <td>${pos.symbol}</td>
                            <td>${pos.quantity}</td>
                            <td>$${pos.avg_price.toFixed(2)}</td>
                            <td class="${pos.unrealized_pnl >= 0 ? 'positive' : 'negative'}">
                                $${pos.unrealized_pnl.toFixed(2)}
                            </td>
                            <td>
                                <button class="btn btn-danger" onclick="closePosition('${pos.symbol}')">Close</button>
                            </td>
                        </tr>
                    `).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="5">No live positions</td></tr>';
                }
            } catch (error) {
                console.error('Error loading positions:', error);
            }
        }

        async function loadLeeMethodSignals() {
            try {
                const response = await fetch('/api/v1/lee_method/signals');
                const data = await response.json();

                const tbody = document.getElementById('leeSignalsTable');
                if (data.signals && data.signals.length > 0) {
                    tbody.innerHTML = data.signals.map(signal => `
                        <tr>
                            <td>${signal.symbol}</td>
                            <td>${signal.signal_type}</td>
                            <td>${(signal.confidence * 100).toFixed(1)}%</td>
                            <td>$${signal.entry_price.toFixed(2)}</td>
                            <td>$${signal.target_price.toFixed(2)}</td>
                            <td>$${signal.stop_loss.toFixed(2)}</td>
                            <td>
                                <button class="btn btn-primary" onclick="tradeOnSignal('${signal.symbol}', '${signal.signal_type}')">Trade</button>
                            </td>
                        </tr>
                    `).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="7">No active Lee Method signals</td></tr>';
                }
            } catch (error) {
                console.error('Error loading Lee Method signals:', error);
                document.getElementById('leeSignalsTable').innerHTML = '<tr><td colspan="7">Error loading signals</td></tr>';
            }
        }

        async function optimizePortfolio() {
            try {
                const response = await fetch('/api/v1/portfolio/optimize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.success) {
                    alert('Portfolio optimization completed successfully!');
                    loadDashboardData();
                } else {
                    alert(`Optimization error: ${data.error}`);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error optimizing portfolio. Please try again.');
            }
        }

        async function loadEducationContent(topic) {
            try {
                // Special handling for Lee Method to show 5-point criteria
                if (topic === 'lee-method') {
                    await loadLeeMethodCriteria();
                    return;
                }

                const response = await fetch(`/api/v1/education/content/${topic}`);
                const data = await response.json();

                const contentDiv = document.getElementById('educationContent');
                if (data.content) {
                    contentDiv.innerHTML = `
                        <h3>${data.title}</h3>
                        <div>${data.content}</div>
                    `;
                } else {
                    contentDiv.innerHTML = '<p>Content not available for this topic.</p>';
                }
            } catch (error) {
                console.error('Error loading education content:', error);
                document.getElementById('educationContent').innerHTML = '<p>Error loading content. Please try again.</p>';
            }
        }

        async function loadLeeMethodCriteria() {
            try {
                const response = await fetch('/api/v1/lee_method/criteria');
                const criteria = await response.json();

                const contentDiv = document.getElementById('educationContent');
                let html = `
                    <h3>🎯 ${criteria.name}</h3>
                    <p><strong>Algorithm:</strong> ${criteria.algorithm}</p>
                    <p><strong>Description:</strong> ${criteria.description}</p>
                    <p><strong>Signal Type:</strong> ${criteria.signal_type}</p>

                    <h4>📋 5-Point Detection Criteria:</h4>
                    <div style="margin: 20px 0;">
                `;

                criteria.criteria.forEach(criterion => {
                    html += `
                        <div style="margin: 15px 0; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                            <h5>Point ${criterion.number}: ${criterion.name}</h5>
                            <p><strong>Description:</strong> ${criterion.description}</p>
                            <p><strong>Technical Detail:</strong> ${criterion.detail}</p>
                        </div>
                    `;
                });

                html += `
                    </div>
                    <h4>⚙️ Technical Specifications:</h4>
                    <ul>
                        <li><strong>MACD Settings:</strong> ${criteria.technical_specs.macd_settings}</li>
                        <li><strong>Bollinger Bands:</strong> ${criteria.technical_specs.bollinger_bands}</li>
                        <li><strong>Keltner Channels:</strong> ${criteria.technical_specs.keltner_channels}</li>
                        <li><strong>EMA Periods:</strong> ${criteria.technical_specs.ema_periods.join(', ')}</li>
                        <li><strong>Squeeze Filter:</strong> ${criteria.technical_specs.squeeze_filter}</li>
                    </ul>

                    <h4>✅ Advantages:</h4>
                    <ul>
                `;

                criteria.advantages.forEach(advantage => {
                    html += `<li>${advantage}</li>`;
                });

                html += `
                    </ul>
                `;

                contentDiv.innerHTML = html;
            } catch (error) {
                console.error('Error loading Lee Method criteria:', error);
                document.getElementById('educationContent').innerHTML = '<p>Error loading Lee Method criteria. Please try again.</p>';
            }
        }

        async function refreshLeeMethodSignals() {
            try {
                document.getElementById('leeSignalsTable').innerHTML = '<tr><td colspan="7">Refreshing Lee Method signals...</td></tr>';
                await loadLeeMethodSignals();
            } catch (error) {
                console.error('Error refreshing Lee Method signals:', error);
                document.getElementById('leeSignalsTable').innerHTML = '<tr><td colspan="7">Error refreshing signals</td></tr>';
            }
        }

        function showLeeMethodConfig() {
            document.getElementById('leeConfigModal').style.display = 'block';
        }

        function closeLeeMethodConfig() {
            document.getElementById('leeConfigModal').style.display = 'none';
        }

        function saveLeeMethodConfig() {
            const requireSqueeze = document.getElementById('requireSqueeze').checked;
            const squeezeLookback = parseInt(document.getElementById('squeezeLookback').value);

            // In a real implementation, this would send the config to the server
            console.log('Lee Method Config:', { requireSqueeze, squeezeLookback });
            alert(`Configuration saved:\nRequire Squeeze: ${requireSqueeze}\nLookback: ${squeezeLookback}`);

            closeLeeMethodConfig();
        }

        async function loadMarketData() {
            try {
                const symbols = ['AAPL', 'TSLA', 'NVDA', 'SPY', 'MSFT', 'GOOGL'];

                // Update scanner results
                const scannerTbody = document.getElementById('scannerResults');
                if (scannerTbody) {
                    let scannerHtml = '';

                    for (const symbol of symbols) {
                        try {
                            const response = await fetch(`/api/v1/market_data/${symbol}`);
                            const data = await response.json();

                            if (data.quote) {
                                const changeClass = data.quote.change >= 0 ? 'positive' : 'negative';
                                const changeSign = data.quote.change >= 0 ? '+' : '';
                                const signal = Math.random() > 0.7 ? 'BUY' : Math.random() > 0.5 ? 'SELL' : 'HOLD';

                                scannerHtml += `
                                    <tr>
                                        <td>${symbol}</td>
                                        <td>$${data.quote.price.toFixed(2)}</td>
                                        <td class="${changeClass}">${changeSign}${data.quote.change.toFixed(2)}</td>
                                        <td><span class="btn btn-${signal === 'BUY' ? 'primary' : signal === 'SELL' ? 'danger' : 'secondary'}">${signal}</span></td>
                                    </tr>
                                `;
                            }
                        } catch (error) {
                            console.error(`Error loading ${symbol}:`, error);
                        }
                    }

                    if (scannerHtml) {
                        scannerTbody.innerHTML = scannerHtml;
                    }
                }

            } catch (error) {
                console.error('Error loading market data:', error);
            }
        }

        // Additional utility functions
        async function tradeOnSignal(symbol, signalType) {
            const action = signalType.includes('BUY') || signalType.includes('LONG') ? 'BUY' : 'SELL';

            // Pre-fill trade form and switch to live trading tab
            document.getElementById('tradeSymbol').value = symbol;
            document.getElementById('tradeAction').value = action;
            document.getElementById('tradeQuantity').value = '100';

            // Switch to live trading tab
            const liveTradeTab = document.querySelector('[data-tab="live-trading"]');
            if (liveTradeTab) {
                liveTradeTab.click();
            }
        }

        async function closePosition(symbol) {
            if (confirm(`Are you sure you want to close your position in ${symbol}?`)) {
                try {
                    const response = await fetch(`/api/v1/trading/close_position/${symbol}`, {
                        method: 'POST'
                    });

                    const data = await response.json();

                    if (data.success) {
                        alert('Position closed successfully!');
                        loadLivePositions();
                        loadDashboardData();
                    } else {
                        alert(`Error closing position: ${data.error}`);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Error closing position. Please try again.');
                }
            }
        }

        async function runRiskAssessment() {
            try {
                const response = await fetch('/api/v1/risk/assessment', {
                    method: 'POST'
                });

                const data = await response.json();

                if (data.success) {
                    alert(`Risk Assessment Complete:\nVaR: ${data.var}\nSharpe Ratio: ${data.sharpe_ratio}\nMax Drawdown: ${data.max_drawdown}`);
                } else {
                    alert(`Risk assessment error: ${data.error}`);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error running risk assessment. Please try again.');
            }
        }

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            loadMarketData();
            loadLeeMethodSignals();
            loadDashboardData();
        }, 30000);

        // Auto-refresh live positions every 10 seconds when on live trading tab
        setInterval(() => {
            const activeTab = document.querySelector('.tab-content.active');
            if (activeTab && activeTab.id === 'live-trading') {
                loadLivePositions();
            }
        }, 10000);
    </script>
</body>
</html>
