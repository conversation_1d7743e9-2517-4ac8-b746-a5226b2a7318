"""
A.T.L.A.S. Real-Time Scanner Test Suite
Comprehensive testing of scanner functionality including pattern detection,
real-time updates, API integration, error handling, and performance
"""

import asyncio
import logging
import json
import sys
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import unittest
from unittest.mock import Mock, patch, AsyncMock

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

from atlas_realtime_scanner import AtlasRealtimeScanner, ScannerConfig, <PERSON>anner<PERSON><PERSON>ult
from atlas_lee_method import LeeMethodScanner
from atlas_market_core import AtlasMarketEngine
from models import Quote

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestRealtimeScanner(unittest.TestCase):
    """Test suite for the real-time scanner"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.scanner = AtlasRealtimeScanner()
        self.test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
        
    def tearDown(self):
        """Clean up after tests"""
        if self.scanner.is_running:
            asyncio.run(self.scanner.stop_scanner())

    def test_scanner_initialization(self):
        """Test scanner initialization"""
        logger.info("Testing scanner initialization...")
        
        # Test default configuration
        self.assertIsInstance(self.scanner.config, ScannerConfig)
        self.assertTrue(self.scanner.config.enabled)
        self.assertEqual(self.scanner.config.scan_interval, 30)
        self.assertTrue(self.scanner.config.market_hours_only)
        self.assertEqual(self.scanner.config.min_confidence, 0.6)
        
        # Test scanner components
        self.assertIsInstance(self.scanner.lee_scanner, LeeMethodScanner)
        self.assertIsInstance(self.scanner.market_engine, AtlasMarketEngine)
        
        logger.info("✅ Scanner initialization test passed")

    def test_scanner_config_update(self):
        """Test scanner configuration updates"""
        logger.info("Testing scanner configuration updates...")
        
        # Test configuration updates
        config_updates = {
            'scan_interval': 60,
            'min_confidence': 0.8,
            'market_hours_only': False
        }
        
        result = asyncio.run(self.scanner.update_config(config_updates))
        self.assertTrue(result)
        
        # Verify updates were applied
        self.assertEqual(self.scanner.config.scan_interval, 60)
        self.assertEqual(self.scanner.config.min_confidence, 0.8)
        self.assertFalse(self.scanner.config.market_hours_only)
        
        logger.info("✅ Scanner configuration update test passed")

    @patch('atlas_realtime_scanner.AtlasMarketEngine')
    @patch('atlas_realtime_scanner.LeeMethodScanner')
    def test_scanner_startup_shutdown(self, mock_lee_scanner, mock_market_engine):
        """Test scanner startup and shutdown"""
        logger.info("Testing scanner startup and shutdown...")
        
        # Mock the initialization methods
        mock_market_engine.return_value.initialize = AsyncMock()
        mock_lee_scanner.return_value.initialize = AsyncMock()
        
        # Test initialization
        result = asyncio.run(self.scanner.initialize())
        self.assertTrue(result)
        
        # Test starting scanner
        result = asyncio.run(self.scanner.start_scanner())
        self.assertTrue(result)
        self.assertTrue(self.scanner.is_running)
        
        # Test stopping scanner
        result = asyncio.run(self.scanner.stop_scanner())
        self.assertTrue(result)
        self.assertFalse(self.scanner.is_running)
        
        logger.info("✅ Scanner startup/shutdown test passed")

    def test_market_hours_detection(self):
        """Test market hours detection logic"""
        logger.info("Testing market hours detection...")
        
        # Test during market hours (10:00 AM)
        with patch('atlas_realtime_scanner.datetime') as mock_datetime:
            mock_time = Mock()
            mock_time.time.return_value = time(10, 0)  # 10:00 AM
            mock_datetime.now.return_value = mock_time
            
            self.scanner.config.market_hours_only = True
            should_scan = self.scanner._should_scan()
            # Note: This will depend on other conditions too, but market hours should be True
            
        # Test outside market hours (6:00 PM)
        with patch('atlas_realtime_scanner.datetime') as mock_datetime:
            mock_time = Mock()
            mock_time.time.return_value = time(18, 0)  # 6:00 PM
            mock_datetime.now.return_value = mock_time
            
            self.scanner.config.market_hours_only = True
            should_scan = self.scanner._should_scan()
            self.assertFalse(should_scan)
        
        logger.info("✅ Market hours detection test passed")

    @patch('atlas_realtime_scanner.AtlasMarketEngine')
    def test_market_data_integration(self, mock_market_engine):
        """Test market data integration with fallback"""
        logger.info("Testing market data integration...")
        
        # Mock successful market data retrieval
        mock_quote = Quote(
            symbol='AAPL',
            price=150.0,
            change=2.5,
            change_percent=1.67,
            volume=1000000,
            timestamp=datetime.now()
        )
        
        mock_market_engine.return_value.get_quote = AsyncMock(return_value=mock_quote)
        self.scanner.market_engine = mock_market_engine.return_value
        
        # Test market data retrieval
        result = asyncio.run(self.scanner._get_market_data('AAPL'))
        
        self.assertIsNotNone(result)
        self.assertEqual(result['price'], 150.0)
        self.assertEqual(result['change'], 2.5)
        self.assertEqual(result['change_percent'], 1.67)
        
        logger.info("✅ Market data integration test passed")

    @patch('aiohttp.ClientSession')
    def test_fmp_fallback_data(self, mock_session):
        """Test FMP API fallback data retrieval"""
        logger.info("Testing FMP fallback data retrieval...")
        
        # Mock FMP API response
        mock_response_data = [{
            'symbol': 'AAPL',
            'price': 150.0,
            'change': 2.5,
            'changesPercentage': 1.67,
            'volume': 1000000
        }]
        
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=mock_response_data)
        
        mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
        
        # Test fallback data retrieval
        result = asyncio.run(self.scanner._get_fmp_fallback_data('AAPL'))
        
        self.assertIsNotNone(result)
        self.assertEqual(result['price'], 150.0)
        self.assertEqual(result['change'], 2.5)
        self.assertEqual(result['change_percent'], 1.67)
        
        logger.info("✅ FMP fallback data test passed")

    def test_scanner_result_processing(self):
        """Test scanner result processing and storage"""
        logger.info("Testing scanner result processing...")
        
        # Create test scanner result
        test_result = ScannerResult(
            symbol='AAPL',
            price=150.0,
            change=2.5,
            change_percent=1.67,
            pattern_found=True,
            pattern_type='lee_method_ttm_squeeze',
            confidence=0.85,
            signal_strength='STRONG',
            timestamp=datetime.now().isoformat(),
            histogram_current=-0.5,
            ema5_trend=True,
            ema8_trend=True,
            momentum_trend=True,
            squeeze_active=True
        )
        
        # Process the result
        self.scanner._process_scan_result(test_result)
        
        # Verify result was stored
        self.assertIn('AAPL', self.scanner.active_results)
        stored_result = self.scanner.active_results['AAPL']
        self.assertEqual(stored_result.symbol, 'AAPL')
        self.assertEqual(stored_result.confidence, 0.85)
        
        logger.info("✅ Scanner result processing test passed")

    def test_websocket_connection_management(self):
        """Test WebSocket connection management"""
        logger.info("Testing WebSocket connection management...")
        
        # Mock WebSocket connections
        mock_ws1 = Mock()
        mock_ws2 = Mock()
        
        # Test adding connections
        self.scanner.add_websocket_connection(mock_ws1)
        self.scanner.add_websocket_connection(mock_ws2)
        
        self.assertEqual(len(self.scanner.websocket_connections), 2)
        self.assertIn(mock_ws1, self.scanner.websocket_connections)
        self.assertIn(mock_ws2, self.scanner.websocket_connections)
        
        # Test removing connections
        self.scanner.remove_websocket_connection(mock_ws1)
        self.assertEqual(len(self.scanner.websocket_connections), 1)
        self.assertNotIn(mock_ws1, self.scanner.websocket_connections)
        
        logger.info("✅ WebSocket connection management test passed")

    def test_api_rate_limiting(self):
        """Test API rate limiting functionality"""
        logger.info("Testing API rate limiting...")
        
        # Set low rate limit for testing
        self.scanner.config.api_rate_limit = 5
        
        # Simulate API calls
        self.scanner.api_calls_per_minute = 6  # Exceed limit
        
        # Should not scan when rate limit exceeded
        should_scan = self.scanner._should_scan()
        self.assertFalse(should_scan)
        
        # Reset API calls
        self.scanner.api_calls_per_minute = 0
        self.scanner.config.enabled = True
        self.scanner.config.market_hours_only = False
        
        # Should scan when under rate limit
        should_scan = self.scanner._should_scan()
        self.assertTrue(should_scan)
        
        logger.info("✅ API rate limiting test passed")

    def test_scanner_status_reporting(self):
        """Test scanner status reporting"""
        logger.info("Testing scanner status reporting...")
        
        # Get scanner status
        status = self.scanner.get_scanner_status()
        
        # Verify status structure
        self.assertIn('running', status)
        self.assertIn('market_hours', status)
        self.assertIn('scan_count', status)
        self.assertIn('active_results_count', status)
        self.assertIn('symbols_monitored', status)
        self.assertIn('config', status)
        
        # Verify config is included
        self.assertIsInstance(status['config'], dict)
        self.assertIn('enabled', status['config'])
        self.assertIn('scan_interval', status['config'])
        
        logger.info("✅ Scanner status reporting test passed")

class TestLeeMethodPatternDetection(unittest.TestCase):
    """Test suite for Lee Method pattern detection"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.lee_scanner = LeeMethodScanner()
        
    def test_pattern_detection_algorithm(self):
        """Test the 5-point TTM Squeeze pattern detection"""
        logger.info("Testing Lee Method pattern detection algorithm...")
        
        # This would require creating mock historical data
        # For now, we'll test the method exists and handles errors gracefully
        
        import pandas as pd
        
        # Create minimal test data
        test_data = pd.DataFrame({
            'close': [100, 101, 102, 103, 104],
            'high': [101, 102, 103, 104, 105],
            'low': [99, 100, 101, 102, 103],
            'volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        # Test with insufficient data
        result = self.lee_scanner.detect_lee_method_pattern(test_data)
        # Should return None for insufficient data
        self.assertIsNone(result)
        
        logger.info("✅ Lee Method pattern detection test passed")

def run_performance_test():
    """Run performance test for scanner"""
    logger.info("Running performance test...")
    
    scanner = AtlasRealtimeScanner()
    
    # Test with limited symbols for performance
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    scanner.scan_symbols = test_symbols
    
    start_time = time.time()
    
    # Simulate a scan cycle (without actual API calls)
    with patch.object(scanner, '_scan_single_symbol', return_value=None):
        scanner._perform_scan_cycle()
    
    end_time = time.time()
    scan_duration = end_time - start_time
    
    logger.info(f"Performance test completed in {scan_duration:.2f} seconds")
    logger.info(f"Scanned {len(test_symbols)} symbols")
    logger.info(f"Average time per symbol: {scan_duration/len(test_symbols):.3f} seconds")
    
    # Performance should be reasonable
    assert scan_duration < 30, f"Scan took too long: {scan_duration} seconds"
    
    logger.info("✅ Performance test passed")

def run_integration_test():
    """Run integration test with mocked components"""
    logger.info("Running integration test...")
    
    async def integration_test():
        scanner = AtlasRealtimeScanner()
        
        # Mock the components to avoid actual API calls
        with patch.object(scanner.market_engine, 'initialize', new_callable=AsyncMock):
            with patch.object(scanner.lee_scanner, 'initialize', new_callable=AsyncMock):
                # Test full initialization
                result = await scanner.initialize()
                assert result, "Scanner initialization failed"
                
                # Test configuration update
                config_update = {'scan_interval': 45, 'min_confidence': 0.7}
                result = await scanner.update_config(config_update)
                assert result, "Configuration update failed"
                
                # Test getting active results
                results = await scanner.get_active_results()
                assert isinstance(results, list), "Active results should be a list"
                
                logger.info("✅ Integration test passed")
    
    asyncio.run(integration_test())

if __name__ == '__main__':
    logger.info("🚀 Starting A.T.L.A.S. Real-Time Scanner Test Suite")
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance test
    run_performance_test()
    
    # Run integration test
    run_integration_test()
    
    logger.info("✅ All tests completed successfully!")
