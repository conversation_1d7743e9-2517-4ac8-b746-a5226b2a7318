#!/usr/bin/env python3
"""
Test the fixed scanner with relaxed criteria
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_lee_method import LeeMethodScanner
from atlas_realtime_scanner import AtlasRealtimeScanner

async def test_fixed_scanner():
    """Test the scanner with new flexible criteria"""
    print("🔧 Testing Fixed Scanner with Relaxed Criteria")
    print("=" * 60)
    
    # Initialize scanner
    scanner = LeeMethodScanner()
    
    # Test different sensitivity configurations
    configurations = [
        {
            'name': 'High Sensitivity',
            'use_flexible_patterns': True,
            'min_confidence_threshold': 0.3,
            'pattern_sensitivity': 0.8,
            'allow_weak_signals': True
        },
        {
            'name': 'Medium Sensitivity',
            'use_flexible_patterns': True,
            'min_confidence_threshold': 0.4,
            'pattern_sensitivity': 0.7,
            'allow_weak_signals': True
        },
        {
            'name': 'Conservative',
            'use_flexible_patterns': True,
            'min_confidence_threshold': 0.5,
            'pattern_sensitivity': 0.6,
            'allow_weak_signals': False
        },
        {
            'name': 'Original Strict',
            'use_flexible_patterns': False,
            'min_confidence_threshold': 0.6,
            'pattern_sensitivity': 0.5,
            'allow_weak_signals': False
        }
    ]
    
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMZN', 'META', 'NFLX', 'AMD', 'CRM']
    
    for config in configurations:
        print(f"\n📊 Testing {config['name']} Configuration:")
        print(f"   Flexible patterns: {config['use_flexible_patterns']}")
        print(f"   Min confidence: {config['min_confidence_threshold']}")
        print(f"   Pattern sensitivity: {config['pattern_sensitivity']}")
        print(f"   Allow weak signals: {config['allow_weak_signals']}")
        
        # Configure scanner
        scanner.configure_pattern_sensitivity(
            use_flexible_patterns=config['use_flexible_patterns'],
            min_confidence_threshold=config['min_confidence_threshold'],
            pattern_sensitivity=config['pattern_sensitivity'],
            allow_weak_signals=config['allow_weak_signals']
        )
        
        # Test scanning
        signals_found = 0
        signal_details = []
        
        for symbol in test_symbols:
            try:
                signal = await scanner.scan_symbol(symbol)
                if signal:
                    signals_found += 1
                    signal_details.append({
                        'symbol': symbol,
                        'confidence': signal.confidence,
                        'signal_strength': getattr(signal, 'signal_strength', 'unknown'),
                        'signal_direction': signal.signal_direction
                    })
                    print(f"   ✅ {symbol}: {signal.signal_direction} (confidence: {signal.confidence:.2f}, strength: {getattr(signal, 'signal_strength', 'unknown')})")
                else:
                    print(f"   ❌ {symbol}: No pattern")
            except Exception as e:
                print(f"   ❌ {symbol}: Error - {e}")
        
        print(f"\n   📈 Results: {signals_found}/{len(test_symbols)} signals found")
        
        if signal_details:
            avg_confidence = sum(s['confidence'] for s in signal_details) / len(signal_details)
            print(f"   📊 Average confidence: {avg_confidence:.2f}")
            
            strength_counts = {}
            for s in signal_details:
                strength = s['signal_strength']
                strength_counts[strength] = strength_counts.get(strength, 0) + 1
            print(f"   💪 Signal strengths: {strength_counts}")

async def test_batch_scanning():
    """Test batch scanning with the fixed scanner"""
    print("\n🔄 Testing Batch Scanning with Fixed Scanner:")
    print("=" * 50)
    
    scanner = LeeMethodScanner()
    
    # Configure for optimal results
    scanner.configure_pattern_sensitivity(
        use_flexible_patterns=True,
        min_confidence_threshold=0.4,
        pattern_sensitivity=0.7,
        allow_weak_signals=True
    )
    
    # Test with larger batch
    test_symbols = [
        'AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMZN', 'META', 'NFLX', 'AMD', 'CRM',
        'JPM', 'JNJ', 'PG', 'UNH', 'HD', 'MA', 'DIS', 'PYPL', 'ADBE', 'INTC',
        'VZ', 'KO', 'PFE', 'WMT', 'BAC', 'XOM', 'T', 'CVX', 'ABBV', 'MRK'
    ]
    
    print(f"Testing batch scan with {len(test_symbols)} symbols...")
    
    try:
        signals = await scanner.scan_multiple_symbols(test_symbols)
        
        print(f"✅ Batch scan completed: {len(signals)} signals found")
        
        if signals:
            print("\n📈 Found Signals:")
            for signal in signals[:10]:  # Show first 10
                print(f"   {signal.symbol}: {signal.signal_direction} "
                      f"(confidence: {signal.confidence:.2f})")
            
            if len(signals) > 10:
                print(f"   ... and {len(signals) - 10} more signals")
            
            # Statistics
            avg_confidence = sum(s.confidence for s in signals) / len(signals)
            print(f"\n📊 Statistics:")
            print(f"   Total signals: {len(signals)}")
            print(f"   Success rate: {len(signals)}/{len(test_symbols)} ({len(signals)/len(test_symbols)*100:.1f}%)")
            print(f"   Average confidence: {avg_confidence:.2f}")
        
        return len(signals)
        
    except Exception as e:
        print(f"❌ Batch scan failed: {e}")
        import traceback
        traceback.print_exc()
        return 0

async def test_realtime_scanner_integration():
    """Test integration with the realtime scanner"""
    print("\n🔄 Testing Real-time Scanner Integration:")
    print("=" * 45)
    
    try:
        realtime_scanner = AtlasRealtimeScanner()
        
        # Update configuration to use relaxed criteria
        realtime_scanner.config.min_confidence = 0.4
        realtime_scanner.config.require_squeeze = False
        realtime_scanner.config.pattern_sensitivity = 0.7
        
        print(f"✅ Real-time scanner configured:")
        print(f"   Min confidence: {realtime_scanner.config.min_confidence}")
        print(f"   Require squeeze: {realtime_scanner.config.require_squeeze}")
        print(f"   Pattern sensitivity: {realtime_scanner.config.pattern_sensitivity}")
        
        # Configure the underlying Lee Method scanner
        realtime_scanner.lee_scanner.configure_pattern_sensitivity(
            use_flexible_patterns=True,
            min_confidence_threshold=0.4,
            pattern_sensitivity=0.7,
            allow_weak_signals=True
        )
        
        print("✅ Lee Method scanner configured with flexible patterns")
        
        return True
        
    except Exception as e:
        print(f"❌ Real-time scanner integration failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 A.T.L.A.S. Fixed Scanner Test Suite")
    print("=" * 50)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Test individual configurations
        await test_fixed_scanner()
        
        # Test batch scanning
        batch_signals = await test_batch_scanning()
        
        # Test real-time integration
        integration_ok = await test_realtime_scanner_integration()
        
        print("\n" + "=" * 50)
        print("📊 FINAL SUMMARY:")
        print(f"   Batch scan signals: {batch_signals}")
        print(f"   Real-time integration: {'✅ Success' if integration_ok else '❌ Failed'}")
        
        if batch_signals > 0:
            print("\n🎉 SUCCESS: Scanner is now working!")
            print("   ✅ Flexible pattern detection implemented")
            print("   ✅ Configuration options added")
            print("   ✅ Signals are being detected")
            print("   ✅ Real-time integration ready")
        else:
            print("\n⚠️  PARTIAL SUCCESS: Scanner improved but may need further tuning")
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
