#!/usr/bin/env python3
"""
Minimal test to check scanner functionality
"""

import asyncio
import sys
import os

# Add the current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_lee_method import LeeMethodScanner

async def minimal_test():
    """Minimal test"""
    print("🔧 Minimal Scanner Test")
    
    try:
        # Initialize scanner
        print("1. Creating scanner...")
        scanner = LeeMethodScanner()
        print("   ✅ Scanner created")
        
        # Configure
        print("2. Configuring scanner...")
        scanner.configure_pattern_sensitivity(
            use_flexible_patterns=True,
            min_confidence_threshold=0.3,
            pattern_sensitivity=0.8,
            allow_weak_signals=True
        )
        print("   ✅ Scanner configured")
        
        # Test data fetch
        print("3. Testing data fetch...")
        df = await scanner.fetch_historical_data('AAPL', limit=50)
        if df.empty:
            print("   ❌ No data returned")
            return False
        else:
            print(f"   ✅ Got {len(df)} rows of data")
        
        # Test indicator calculation
        print("4. Testing indicators...")
        df_with_indicators = scanner.calculate_lee_method_indicators(df)
        required_cols = ['histogram', 'ema5', 'ema8']
        missing_cols = [col for col in required_cols if col not in df_with_indicators.columns]
        if missing_cols:
            print(f"   ❌ Missing columns: {missing_cols}")
            return False
        else:
            print("   ✅ All indicators calculated")
        
        # Test flexible pattern detection
        print("5. Testing flexible pattern detection...")
        pattern_result = scanner.detect_lee_method_pattern_flexible(df_with_indicators)
        if pattern_result:
            print(f"   ✅ Pattern detection completed")
            print(f"   Pattern found: {pattern_result['pattern_found']}")
            if pattern_result['pattern_found']:
                print(f"   Confidence: {pattern_result['confidence']:.2f}")
        else:
            print("   ❌ Pattern detection failed")
            return False
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(minimal_test())
    print(f"\nResult: {'SUCCESS' if result else 'FAILED'}")
