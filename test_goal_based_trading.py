#!/usr/bin/env python3
"""
Test script to verify A.T.L.A.S. AI understanding of goal-based trading requests
Tests "Make me $X dollars today" type requests to ensure proper capability recognition
"""

import asyncio
import aiohttp
import json
import sys
from typing import Dict, Any

# Test cases for goal-based trading requests
GOAL_BASED_TEST_CASES = [
    {
        "name": "Make Money Goal - $50",
        "message": "Make me $50 today",
        "expected_keywords": ["trading", "strategy", "risk", "analysis", "portfolio", "goal"],
        "expected_capabilities": ["trading_plan", "risk_assessment", "market_analysis"]
    },
    {
        "name": "Make Money Goal - $100",
        "message": "I want to make $100 dollars today, what are my best options?",
        "expected_keywords": ["options", "trading", "strategy", "analysis", "risk", "portfolio"],
        "expected_capabilities": ["strategy_generation", "risk_management", "market_scanning"]
    },
    {
        "name": "Make Money Goal - $500",
        "message": "Help me make $500 today through trading",
        "expected_keywords": ["trading", "strategy", "risk", "capital", "analysis", "plan"],
        "expected_capabilities": ["trading_execution", "risk_calculation", "strategy_planning"]
    },
    {
        "name": "Daily Profit Goal",
        "message": "What's the best way to make money in the market today?",
        "expected_keywords": ["market", "strategy", "analysis", "trading", "opportunities"],
        "expected_capabilities": ["market_analysis", "strategy_recommendation", "scanning"]
    },
    {
        "name": "Specific Amount with Timeline",
        "message": "I need to make $200 by market close today",
        "expected_keywords": ["trading", "strategy", "risk", "timeline", "analysis", "execution"],
        "expected_capabilities": ["time_sensitive_trading", "risk_management", "execution_planning"]
    }
]

async def test_goal_based_request(session: aiohttp.ClientSession, test_case: Dict[str, Any]) -> Dict[str, Any]:
    """Test a goal-based trading request"""
    try:
        payload = {
            "message": test_case["message"],
            "session_id": f"goal_test_{test_case['name'].replace(' ', '_').lower()}"
        }
        
        async with session.post("http://localhost:8080/api/v1/chat", json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return {
                    "test_name": test_case["name"],
                    "success": True,
                    "response": data.get("response", ""),
                    "type": data.get("type", ""),
                    "confidence": data.get("confidence", 0.0),
                    "expected_keywords": test_case["expected_keywords"],
                    "expected_capabilities": test_case["expected_capabilities"]
                }
            else:
                return {
                    "test_name": test_case["name"],
                    "success": False,
                    "error": f"HTTP {response.status}",
                    "expected_keywords": test_case["expected_keywords"],
                    "expected_capabilities": test_case["expected_capabilities"]
                }
    except Exception as e:
        return {
            "test_name": test_case["name"],
            "success": False,
            "error": str(e),
            "expected_keywords": test_case["expected_keywords"],
            "expected_capabilities": test_case["expected_capabilities"]
        }

def analyze_goal_based_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze the AI response for goal-based trading understanding"""
    if not result["success"]:
        return {
            "understands_goal": False,
            "keyword_matches": 0,
            "capability_awareness": False,
            "provides_strategy": False,
            "mentions_risk": False,
            "analysis": f"Request failed: {result.get('error', 'Unknown error')}"
        }
    
    response_text = result["response"].lower()
    expected_keywords = result["expected_keywords"]
    expected_capabilities = result["expected_capabilities"]
    
    # Check for keyword matches
    keyword_matches = sum(1 for keyword in expected_keywords if keyword.lower() in response_text)
    
    # Check for capability awareness
    capability_mentions = sum(1 for cap in expected_capabilities if any(
        cap_word in response_text for cap_word in cap.split('_')
    ))
    
    # Check for strategic thinking
    strategy_indicators = [
        "strategy", "plan", "approach", "method", "steps", "analysis", 
        "recommend", "suggest", "consider", "evaluate", "assess"
    ]
    provides_strategy = any(indicator in response_text for indicator in strategy_indicators)
    
    # Check for risk awareness
    risk_indicators = [
        "risk", "capital", "loss", "position size", "portfolio", "diversification",
        "volatility", "exposure", "management", "careful", "conservative"
    ]
    mentions_risk = any(indicator in response_text for indicator in risk_indicators)
    
    # Check for negative responses
    negative_indicators = [
        "i cannot", "i can't", "i don't have access", "not available", 
        "unable to", "cannot perform", "don't have the ability", "cannot guarantee",
        "not possible", "cannot make money", "cannot promise"
    ]
    has_negative_indicators = any(indicator in response_text for indicator in negative_indicators)
    
    # Check for comprehensive understanding
    comprehensive_indicators = [
        "trading", "market", "analysis", "strategy", "risk", "portfolio"
    ]
    comprehensive_score = sum(1 for indicator in comprehensive_indicators if indicator in response_text)
    
    # Determine goal understanding
    understands_goal = (
        keyword_matches >= len(expected_keywords) * 0.4 and  # At least 40% keyword match
        provides_strategy and  # Shows strategic thinking
        mentions_risk and  # Acknowledges risk
        not has_negative_indicators and  # No negative capability indicators
        comprehensive_score >= 3 and  # Mentions multiple trading concepts
        len(response_text) > 100  # Substantial response
    )
    
    return {
        "understands_goal": understands_goal,
        "keyword_matches": keyword_matches,
        "total_keywords": len(expected_keywords),
        "capability_awareness": capability_mentions > 0,
        "provides_strategy": provides_strategy,
        "mentions_risk": mentions_risk,
        "has_negative_indicators": has_negative_indicators,
        "comprehensive_score": comprehensive_score,
        "response_length": len(response_text),
        "analysis": "Good goal understanding" if understands_goal else "Poor goal understanding"
    }

async def run_goal_based_tests():
    """Run all goal-based trading tests"""
    print("🎯 Testing A.T.L.A.S. AI Goal-Based Trading Understanding")
    print("Testing: 'Make me $X dollars today' type requests")
    print("=" * 70)
    
    async with aiohttp.ClientSession() as session:
        results = []
        
        for i, test_case in enumerate(GOAL_BASED_TEST_CASES, 1):
            print(f"\n{i}. Testing: {test_case['name']}")
            print(f"   Query: '{test_case['message']}'")
            
            result = await test_goal_based_request(session, test_case)
            analysis = analyze_goal_based_response(result)
            
            results.append({**result, **analysis})
            
            if result["success"]:
                print(f"   ✅ Response received (confidence: {result['confidence']:.2f})")
                print(f"   📊 Keywords: {analysis['keyword_matches']}/{analysis['total_keywords']}")
                print(f"   🎯 Goal understanding: {'✅ Good' if analysis['understands_goal'] else '❌ Poor'}")
                print(f"   📋 Provides strategy: {'✅ Yes' if analysis['provides_strategy'] else '❌ No'}")
                print(f"   🛡️ Mentions risk: {'✅ Yes' if analysis['mentions_risk'] else '❌ No'}")
                
                if not analysis['understands_goal']:
                    print(f"   ⚠️  Issues: {analysis['analysis']}")
                    if analysis['has_negative_indicators']:
                        print("   ⚠️  Contains negative capability indicators")
                
                # Show a snippet of the response
                response_snippet = result['response'][:200] + "..." if len(result['response']) > 200 else result['response']
                print(f"   💬 Response: {response_snippet}")
                
            else:
                print(f"   ❌ Request failed: {result.get('error', 'Unknown error')}")
            
            # Small delay between requests
            await asyncio.sleep(2)
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 GOAL-BASED TRADING UNDERSTANDING SUMMARY")
    print("=" * 70)
    
    successful_tests = [r for r in results if r["success"]]
    goal_aware_responses = [r for r in successful_tests if r["understands_goal"]]
    strategic_responses = [r for r in successful_tests if r["provides_strategy"]]
    risk_aware_responses = [r for r in successful_tests if r["mentions_risk"]]
    
    print(f"Total tests: {len(GOAL_BASED_TEST_CASES)}")
    print(f"Successful requests: {len(successful_tests)}")
    print(f"Goal-aware responses: {len(goal_aware_responses)}")
    print(f"Strategic responses: {len(strategic_responses)}")
    print(f"Risk-aware responses: {len(risk_aware_responses)}")
    print(f"Success rate: {len(successful_tests)/len(GOAL_BASED_TEST_CASES)*100:.1f}%")
    print(f"Goal understanding rate: {len(goal_aware_responses)/len(successful_tests)*100:.1f}%" if successful_tests else "N/A")
    print(f"Strategic thinking rate: {len(strategic_responses)/len(successful_tests)*100:.1f}%" if successful_tests else "N/A")
    print(f"Risk awareness rate: {len(risk_aware_responses)/len(successful_tests)*100:.1f}%" if successful_tests else "N/A")
    
    # Detailed results
    print(f"\n📝 DETAILED RESULTS:")
    for result in results:
        status = "✅" if result["success"] and result.get("understands_goal", False) else "❌"
        print(f"{status} {result['test_name']}")
        if result["success"]:
            print(f"    Keywords: {result['keyword_matches']}/{result['total_keywords']}")
            print(f"    Strategy: {'✅' if result['provides_strategy'] else '❌'}")
            print(f"    Risk: {'✅' if result['mentions_risk'] else '❌'}")
            if not result.get("understands_goal", False):
                print(f"    Issue: {result.get('analysis', 'Unknown issue')}")
    
    return results

if __name__ == "__main__":
    try:
        results = asyncio.run(run_goal_based_tests())
        
        # Exit with appropriate code
        successful_tests = [r for r in results if r["success"]]
        goal_aware_responses = [r for r in successful_tests if r.get("understands_goal", False)]
        
        if len(goal_aware_responses) >= len(successful_tests) * 0.8:  # 80% goal understanding threshold
            print("\n🎉 A.T.L.A.S. AI shows excellent goal-based trading understanding!")
            sys.exit(0)
        else:
            print("\n⚠️  A.T.L.A.S. AI needs improvement in goal-based trading understanding.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
