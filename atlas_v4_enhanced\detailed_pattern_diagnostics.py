#!/usr/bin/env python3
"""
Detailed pattern diagnostics to understand why scanner patterns are failing
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_lee_method import LeeMethodScanner

async def detailed_pattern_analysis(symbol: str):
    """Perform detailed analysis of why pattern detection is failing for a symbol"""
    print(f"\n🔬 Detailed Pattern Analysis for {symbol}")
    print("=" * 60)
    
    scanner = LeeMethodScanner()
    
    try:
        # Fetch data
        df = await scanner.fetch_historical_data(symbol, limit=100)
        if df.empty:
            print(f"❌ No data available for {symbol}")
            return
        
        # Calculate indicators
        df_with_indicators = scanner.calculate_lee_method_indicators(df)
        
        # Show recent histogram values
        print("\n📊 Recent Histogram Values (last 10 bars):")
        recent_hist = df_with_indicators['histogram'].tail(10)
        for i, (idx, hist_val) in enumerate(recent_hist.items()):
            marker = "👈 CURRENT" if i == len(recent_hist) - 1 else ""
            print(f"   Bar {i-9:2d}: {hist_val:8.4f} {marker}")
        
        # Analyze histogram decline pattern
        print("\n🔍 Histogram Decline Pattern Analysis:")
        if len(df_with_indicators) >= 4:
            hist_values = df_with_indicators['histogram'].tail(4).values
            print(f"   Last 4 histogram values: {[f'{v:.4f}' for v in hist_values]}")
            
            # Check each decline condition
            decline_1 = hist_values[-2] < hist_values[-3]  # 2nd to last < 3rd to last
            decline_2 = hist_values[-3] < hist_values[-4]  # 3rd to last < 4th to last
            
            print(f"   Decline 1 (bar -2 < bar -3): {hist_values[-2]:.4f} < {hist_values[-3]:.4f} = {decline_1}")
            print(f"   Decline 2 (bar -3 < bar -4): {hist_values[-3]:.4f} < {hist_values[-4]:.4f} = {decline_2}")
            print(f"   Three declining pattern: {decline_1 and decline_2}")
        
        # Analyze histogram rebound signal
        print("\n🔍 Histogram Rebound Signal Analysis:")
        if len(df_with_indicators) >= 2:
            current_hist = df_with_indicators['histogram'].iloc[-1]
            previous_hist = df_with_indicators['histogram'].iloc[-2]
            
            hist_increased = current_hist > previous_hist
            current_negative = current_hist < 0
            previous_negative = previous_hist < 0
            
            print(f"   Current histogram: {current_hist:.4f}")
            print(f"   Previous histogram: {previous_hist:.4f}")
            print(f"   Histogram increased: {current_hist:.4f} > {previous_hist:.4f} = {hist_increased}")
            print(f"   Current negative: {current_hist:.4f} < 0 = {current_negative}")
            print(f"   Previous negative: {previous_hist:.4f} < 0 = {previous_negative}")
            print(f"   Rebound signal: {hist_increased and current_negative and previous_negative}")
        
        # Analyze EMA trends
        print("\n🔍 EMA Trend Analysis:")
        if len(df_with_indicators) >= 8:
            # EMA 5 trend
            ema5_current = df_with_indicators['ema5'].iloc[-1]
            ema5_previous = df_with_indicators['ema5'].iloc[-2]
            ema5_uptrend = ema5_current > ema5_previous
            
            print(f"   EMA 5 current: {ema5_current:.2f}")
            print(f"   EMA 5 previous: {ema5_previous:.2f}")
            print(f"   EMA 5 uptrend: {ema5_current:.2f} > {ema5_previous:.2f} = {ema5_uptrend}")
            
            # EMA 8 trend (3-bar confirmation)
            ema8_current = df_with_indicators['ema8'].iloc[-1]
            ema8_3bars = df_with_indicators['ema8'].iloc[-4]
            ema8_uptrend = ema8_current > ema8_3bars
            
            print(f"   EMA 8 current: {ema8_current:.2f}")
            print(f"   EMA 8 3-bars ago: {ema8_3bars:.2f}")
            print(f"   EMA 8 uptrend: {ema8_current:.2f} > {ema8_3bars:.2f} = {ema8_uptrend}")
        
        # Analyze squeeze state
        print("\n🔍 TTM Squeeze Analysis:")
        if len(df_with_indicators) >= 1:
            squeeze_active = df_with_indicators['squeeze_active'].iloc[-1]
            bb_upper = df_with_indicators['bb_upper'].iloc[-1]
            bb_lower = df_with_indicators['bb_lower'].iloc[-1]
            kc_upper = df_with_indicators['kc_upper'].iloc[-1]
            kc_lower = df_with_indicators['kc_lower'].iloc[-1]
            
            print(f"   Squeeze active: {squeeze_active}")
            print(f"   BB range: {bb_lower:.2f} - {bb_upper:.2f} (width: {bb_upper - bb_lower:.2f})")
            print(f"   KC range: {kc_lower:.2f} - {kc_upper:.2f} (width: {kc_upper - kc_lower:.2f})")
            print(f"   BB inside KC: {bb_upper < kc_upper and bb_lower > kc_lower}")
        
        # Show overall pattern result
        print("\n🎯 Overall Pattern Detection:")
        pattern_result = scanner.detect_lee_method_pattern(df_with_indicators)
        if pattern_result:
            print(f"   Pattern found: {pattern_result['pattern_found']}")
            if pattern_result['pattern_found']:
                print(f"   Confidence: {pattern_result['confidence']:.2f}")
                print(f"   Signal direction: {pattern_result['signal_direction']}")
            else:
                print(f"   Failure reason: Pattern requirements not met")
        else:
            print("   Pattern detection failed completely")
        
        # Suggest what would make this pattern work
        print("\n💡 What Would Make This Pattern Work:")
        suggestions = []
        
        # Check each component
        decline_result = scanner._check_histogram_decline_pattern(df_with_indicators)
        if not decline_result['pattern_found']:
            suggestions.append("Need 3 consecutive declining histogram bars")
        
        rebound_result = scanner._check_histogram_rebound_signal(df_with_indicators)
        if not rebound_result['rebound_found']:
            suggestions.append("Need histogram rebound (current > previous, both negative)")
        
        ema5_uptrend = scanner._check_ema5_uptrend(df_with_indicators)
        if not ema5_uptrend:
            suggestions.append("Need EMA 5 uptrend")
        
        ema8_uptrend = scanner._check_ema8_uptrend(df_with_indicators)
        if not ema8_uptrend:
            suggestions.append("Need EMA 8 uptrend over 3 bars")
        
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion}")
        
        if not suggestions:
            print("   ✅ All individual components are working - check confidence threshold")
        
    except Exception as e:
        print(f"❌ Analysis failed for {symbol}: {e}")
        import traceback
        traceback.print_exc()

async def analyze_pattern_frequency():
    """Analyze how often each pattern component occurs in the market"""
    print("\n📈 Pattern Component Frequency Analysis")
    print("=" * 50)
    
    scanner = LeeMethodScanner()
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMZN', 'META', 'NFLX', 'AMD', 'CRM',
                   'JPM', 'JNJ', 'PG', 'UNH', 'HD', 'MA', 'DIS', 'PYPL', 'ADBE', 'CRM']
    
    component_stats = {
        'histogram_decline': 0,
        'histogram_rebound': 0,
        'ema5_uptrend': 0,
        'ema8_uptrend': 0,
        'squeeze_active': 0,
        'full_pattern': 0
    }
    
    total_symbols = len(test_symbols)
    
    for symbol in test_symbols:
        try:
            df = await scanner.fetch_historical_data(symbol, limit=100)
            if df.empty:
                continue
            
            df_with_indicators = scanner.calculate_lee_method_indicators(df)
            
            # Check each component
            decline_result = scanner._check_histogram_decline_pattern(df_with_indicators)
            if decline_result['pattern_found']:
                component_stats['histogram_decline'] += 1
            
            rebound_result = scanner._check_histogram_rebound_signal(df_with_indicators)
            if rebound_result['rebound_found']:
                component_stats['histogram_rebound'] += 1
            
            if scanner._check_ema5_uptrend(df_with_indicators):
                component_stats['ema5_uptrend'] += 1
            
            if scanner._check_ema8_uptrend(df_with_indicators):
                component_stats['ema8_uptrend'] += 1
            
            if df_with_indicators['squeeze_active'].iloc[-1]:
                component_stats['squeeze_active'] += 1
            
            pattern_result = scanner.detect_lee_method_pattern(df_with_indicators)
            if pattern_result and pattern_result['pattern_found']:
                component_stats['full_pattern'] += 1
                
        except Exception as e:
            print(f"Error analyzing {symbol}: {e}")
    
    print(f"\nResults from {total_symbols} symbols:")
    for component, count in component_stats.items():
        percentage = (count / total_symbols) * 100
        print(f"   {component.replace('_', ' ').title()}: {count}/{total_symbols} ({percentage:.1f}%)")
    
    return component_stats

async def main():
    """Main diagnostic function"""
    print("🔬 A.T.L.A.S. Detailed Pattern Diagnostics")
    print("=" * 50)
    
    # Analyze specific symbols in detail
    test_symbols = ['AAPL', 'MSFT', 'GOOGL']
    
    for symbol in test_symbols:
        await detailed_pattern_analysis(symbol)
    
    # Analyze pattern frequency across market
    await analyze_pattern_frequency()
    
    print("\n📋 Summary:")
    print("   The pattern detection is extremely restrictive, requiring:")
    print("   1. Exactly 3 consecutive declining histogram bars")
    print("   2. Histogram rebound (current > previous, both negative)")
    print("   3. EMA 5 uptrend (current > previous)")
    print("   4. EMA 8 uptrend (current > 3 bars ago)")
    print("   5. All conditions must be met simultaneously")
    print("\n   This explains why no patterns are being found!")

if __name__ == "__main__":
    asyncio.run(main())
