#!/usr/bin/env python3
"""
Debug script to identify why the scanner is returning no results
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_lee_method import LeeMethodScanner
from atlas_realtime_scanner import AtlasRealtimeScanner, ScannerConfig
from sp500_symbols import get_sp500_symbols, get_core_sp500_symbols

async def debug_scanner_issues():
    """Debug scanner issues step by step"""
    print("🔍 Debugging Scanner Issues")
    print("=" * 50)
    
    # Initialize scanner
    scanner = LeeMethodScanner()
    
    # Test 1: Check API connection and data fetching
    print("\n1️⃣ Testing API Connection and Data Fetching:")
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    for symbol in test_symbols:
        try:
            df = await scanner.fetch_historical_data(symbol, limit=50)
            if df.empty:
                print(f"   ❌ {symbol}: No data returned")
            else:
                print(f"   ✅ {symbol}: {len(df)} rows of data")
                
                # Check data quality
                latest_price = df.iloc[-1]['close']
                print(f"      Latest price: ${latest_price:.2f}")
                
        except Exception as e:
            print(f"   ❌ {symbol}: Error - {e}")
    
    # Test 2: Check pattern detection with real data
    print("\n2️⃣ Testing Pattern Detection with Real Data:")
    
    for symbol in test_symbols[:3]:  # Test first 3 symbols
        try:
            df = await scanner.fetch_historical_data(symbol, limit=100)
            if df.empty:
                continue
                
            # Calculate indicators
            df_with_indicators = scanner.calculate_lee_method_indicators(df)
            
            # Check individual pattern components
            print(f"\n   📊 {symbol} Pattern Analysis:")
            
            # Check histogram decline pattern
            decline_result = scanner._check_histogram_decline_pattern(df_with_indicators)
            print(f"      Histogram Decline: {decline_result['pattern_found']}")
            if decline_result['pattern_found']:
                print(f"         Decline strength: {decline_result.get('decline_strength', 'N/A')}")
            
            # Check histogram rebound
            rebound_result = scanner._check_histogram_rebound_signal(df_with_indicators)
            print(f"      Histogram Rebound: {rebound_result['rebound_found']}")
            if rebound_result['rebound_found']:
                print(f"         Improvement: {rebound_result.get('improvement', 'N/A')}")
            
            # Check EMA trends
            ema5_uptrend = scanner._check_ema5_uptrend(df_with_indicators)
            ema8_uptrend = scanner._check_ema8_uptrend(df_with_indicators)
            print(f"      EMA 5 Uptrend: {ema5_uptrend}")
            print(f"      EMA 8 Uptrend: {ema8_uptrend}")
            
            # Check squeeze filter
            squeeze_ok = scanner._check_squeeze_filter(df_with_indicators)
            print(f"      Squeeze Filter: {squeeze_ok}")
            
            # Full pattern detection
            pattern_result = scanner.detect_lee_method_pattern(df_with_indicators)
            if pattern_result:
                print(f"      Pattern Found: {pattern_result['pattern_found']}")
                if pattern_result['pattern_found']:
                    print(f"         Confidence: {pattern_result['confidence']:.2f}")
                    print(f"         Signal: {pattern_result['signal_direction']}")
            else:
                print(f"      Pattern Detection: Failed")
                
        except Exception as e:
            print(f"   ❌ {symbol}: Pattern detection error - {e}")
    
    # Test 3: Check configuration settings
    print("\n3️⃣ Checking Configuration Settings:")
    
    # Check scanner config
    realtime_scanner = AtlasRealtimeScanner()
    config = realtime_scanner.config
    
    print(f"   Min Confidence: {config.min_confidence}")
    print(f"   Require Squeeze: {config.require_squeeze}")
    print(f"   Pattern Sensitivity: {config.pattern_sensitivity}")
    print(f"   Market Hours Only: {config.market_hours_only}")
    
    # Check Lee Method scanner config
    lee_config = scanner.get_ttm_squeeze_config()
    print(f"   Lee Method Require Squeeze: {lee_config['require_squeeze']}")
    
    # Test 4: Test with relaxed criteria
    print("\n4️⃣ Testing with Relaxed Criteria:")
    
    # Temporarily relax the squeeze requirement
    original_require_squeeze = scanner.require_squeeze
    scanner.configure_squeeze_filter(require_squeeze=False, squeeze_lookback=0)
    
    print("   ✓ Disabled squeeze requirement")
    
    # Test with lower confidence threshold
    original_min_confidence = config.min_confidence
    config.min_confidence = 0.3  # Lower threshold
    
    print(f"   ✓ Lowered confidence threshold to {config.min_confidence}")
    
    # Re-test pattern detection
    found_patterns = 0
    for symbol in test_symbols:
        try:
            signal = await scanner.scan_symbol(symbol)
            if signal:
                found_patterns += 1
                print(f"   ✅ {symbol}: Pattern found! Confidence: {signal.confidence:.2f}")
            else:
                print(f"   ❌ {symbol}: No pattern")
        except Exception as e:
            print(f"   ❌ {symbol}: Error - {e}")
    
    print(f"\n   📊 Results with relaxed criteria: {found_patterns}/{len(test_symbols)} patterns found")
    
    # Restore original settings
    scanner.configure_squeeze_filter(require_squeeze=original_require_squeeze, squeeze_lookback=0)
    config.min_confidence = original_min_confidence
    
    # Test 5: Check symbol list
    print("\n5️⃣ Checking Symbol Lists:")
    
    sp500_symbols = get_sp500_symbols()
    core_symbols = get_core_sp500_symbols()
    
    print(f"   S&P 500 symbols: {len(sp500_symbols)}")
    print(f"   Core symbols: {len(core_symbols)}")
    print(f"   First 10 S&P 500: {sp500_symbols[:10]}")
    
    return found_patterns

async def test_batch_scanning():
    """Test batch scanning functionality"""
    print("\n🔄 Testing Batch Scanning:")
    print("=" * 30)
    
    scanner = LeeMethodScanner()
    
    # Test with small batch
    test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMZN', 'META', 'NFLX', 'AMD', 'CRM']
    
    print(f"Testing batch scan with {len(test_symbols)} symbols...")
    
    try:
        # Use the scan_multiple_symbols method
        signals = await scanner.scan_multiple_symbols(test_symbols)
        
        print(f"✅ Batch scan completed: {len(signals)} signals found")
        
        for signal in signals:
            print(f"   📈 {signal.symbol}: {signal.signal_direction} (confidence: {signal.confidence:.2f})")
            
        return len(signals)
        
    except Exception as e:
        print(f"❌ Batch scan failed: {e}")
        import traceback
        traceback.print_exc()
        return 0

async def main():
    """Main debug function"""
    print("🚀 A.T.L.A.S. Scanner Debug Tool")
    print("=" * 40)
    
    try:
        # Debug individual issues
        patterns_found = await debug_scanner_issues()
        
        # Test batch scanning
        batch_signals = await test_batch_scanning()
        
        print("\n📊 Summary:")
        print(f"   Individual tests: {patterns_found} patterns found")
        print(f"   Batch scan: {batch_signals} signals found")
        
        if patterns_found == 0 and batch_signals == 0:
            print("\n🔧 Recommendations:")
            print("   1. Check API key configuration")
            print("   2. Verify market data is available")
            print("   3. Consider relaxing pattern criteria")
            print("   4. Check if market is open (if market_hours_only=True)")
            print("   5. Review minimum confidence thresholds")
        else:
            print("\n✅ Scanner is working - patterns can be detected!")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
