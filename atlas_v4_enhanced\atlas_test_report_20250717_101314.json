{"timestamp": "2025-07-17T10:13:14.295050", "duration": 30.530189275741577, "summary": {"total_tests": 18, "passed": 12, "failed": 6}, "results": [{"name": "Database Connectivity", "category": "Backend", "status": "PASSED", "message": "All 6 databases connected successfully", "details": {"atlas.db": "Connected", "atlas_memory.db": "Connected", "atlas_rag.db": "Connected", "atlas_compliance.db": "Connected", "atlas_feedback.db": "Connected", "atlas_enhanced_memory.db": "Connected"}, "errors": [], "warnings": [], "performance": {"duration": 0.0002052783966064453}}, {"name": "Configuration Loading", "category": "Backend", "status": "PASSED", "message": "Configuration loaded successfully", "details": {"API Keys": {"OpenAI": true, "Alpaca": true, "FMP": true, "Predicto": true}, "Environment": "development", "Port": 8080}, "errors": [], "warnings": [], "performance": {"duration": 7.867813110351562e-06}}, {"name": "Core Engine Initialization", "category": "Backend", "status": "PASSED", "message": "All engines initialized successfully", "details": {"Engines": {"database": "active", "utils": "active", "market": "active", "risk": "active", "trading": "active", "education": "active", "ai": "active", "lee_method": "initialized"}, "Active Engines": "8/8"}, "errors": [], "warnings": [], "performance": {"duration": 1.0960333347320557}}, {"name": "AI Core Functionality", "category": "Backend", "status": "FAILED", "message": "No AI response generated", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 0.00015044212341308594}}, {"name": "Market Data Access", "category": "Backend", "status": "FAILED", "message": "Unable to fetch market data", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 0.2039482593536377}}, {"name": "<PERSON>", "category": "Backend", "status": "FAILED", "message": "Scanner error: 'LeeMethodScanner' object has no attribute 'detect_pattern'", "details": {}, "errors": ["'LeeMethodScanner' object has no attribute 'detect_pattern'"], "warnings": [], "performance": {"duration": 0.0003306865692138672}}, {"name": "Health Check API", "category": "Integration", "status": "PASSED", "message": "Health endpoint responsive", "details": {"Status": "healthy", "Version": "4.0.0"}, "errors": [], "warnings": [], "performance": {"duration": 2.014023780822754}}, {"name": "Chat API", "category": "Integration", "status": "PASSED", "message": "Chat API functional", "details": {"Response Type": "greeting", "Confidence": 1.0}, "errors": [], "warnings": [], "performance": {"duration": 2.045675039291382}}, {"name": "Market Quote API", "category": "Integration", "status": "FAILED", "message": "HTTP 404", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 2.0155832767486572}}, {"name": "Scanner Status API", "category": "Integration", "status": "PASSED", "message": "Scanner API accessible", "details": {"Running": true, "Symbols Monitored": 30}, "errors": [], "warnings": [], "performance": {"duration": 2.046431064605713}}, {"name": "Portfolio API", "category": "Integration", "status": "PASSED", "message": "Portfolio API functional", "details": {"Cash Balance": "$0.00", "Positions": 0}, "errors": [], "warnings": [], "performance": {"duration": 2.0192182064056396}}, {"name": "Risk Assessment API", "category": "Integration", "status": "FAILED", "message": "HTTP 404", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 2.037839889526367}}, {"name": "Web Interface Loading", "category": "Frontend", "status": "PASSED", "message": "Web interface accessible", "details": {"Content Type": "text/html; charset=utf-8", "Page Size": "33691 bytes", "UI Elements": {"Chat Interface": true, "Scanner Panel": true, "Lee Method Scanner": true, "Input Field": false}}, "errors": [], "warnings": ["Missing UI elements: Input Field"], "performance": {"duration": 2.0456104278564453}}, {"name": "WebSocket Scanner Connection", "category": "Frontend", "status": "FAILED", "message": "WebSocket connection failed", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 2.000912666320801}}, {"name": "Static Assets Loading", "category": "Frontend", "status": "PASSED", "message": "Static assets served correctly", "details": {"/static/atlas_interface.html": "✓ Loaded", "/static/requirements.txt": "✓ Loaded"}, "errors": ["WebSocket error: 'NoneType' object has no attribute 'sock'"], "warnings": [], "performance": {"duration": 4.16208291053772}}, {"name": "Trading Analysis Workflow", "category": "End-to-End", "status": "PASSED", "message": "Trading analysis workflow successful", "details": {"Step 1": "Analysis generated", "6-Point Format": {"Why This Trade": true, "Win/Loss": true, "Money In/Out": true, "Stop Plans": true, "Market Context": true, "Confidence": true}, "Format Compliance": "100%"}, "errors": [], "warnings": [], "performance": {"duration": 2.707463026046753}}, {"name": "Scanner to Signal Display", "category": "End-to-End", "status": "PASSED", "message": "Scanner workflow operational", "details": {"Scanner Running": true, "Signals Found": 0}, "errors": [], "warnings": [], "performance": {"duration": 4.090535879135132}}, {"name": "Educational Query Workflow", "category": "End-to-End", "status": "PASSED", "message": "Response generated (limited educational content)", "details": {"Educational Content": {"Risk Explanation": true, "2% Rule": false, "Example": false, "Educational Tone": false}}, "errors": [], "warnings": [], "performance": {"duration": 2.0324289798736572}}]}