#!/usr/bin/env python3
"""
Debug script to analyze Lee Method signal price calculations
"""

import asyncio
import json
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_lee_method import LeeMethodScanner

async def debug_signal_prices():
    """Debug the price calculation issue"""
    print("🔍 Debugging Lee Method Signal Price Calculations")
    print("=" * 60)
    
    # Initialize scanner
    scanner = LeeMethodScanner()
    
    # Test symbols that showed incorrect prices
    test_symbols = ['AMD', 'SQ']
    
    for symbol in test_symbols:
        print(f"\n📊 Analyzing {symbol}...")
        
        try:
            # Get signal
            signal = await scanner.scan_symbol(symbol)
            
            if signal:
                print(f"✅ Signal detected for {symbol}")
                print(f"   Signal Type: {signal.signal_type}")
                print(f"   Entry Price: ${signal.entry_price:.2f}")
                print(f"   Target Price: ${signal.target_price:.2f}")
                print(f"   Stop Loss: ${signal.stop_loss:.2f}")
                print(f"   Confidence: {signal.confidence:.2f}")
                
                # Manual calculation check
                entry = signal.entry_price
                risk_percent = scanner.default_risk_percent  # Should be 2.0
                reward_ratio = scanner.default_reward_ratio  # Should be 2.0
                
                print(f"\n🧮 Manual Calculation Check:")
                print(f"   Risk Percent: {risk_percent}%")
                print(f"   Reward Ratio: {reward_ratio}:1")
                
                if signal.signal_type == 'bullish_momentum':
                    expected_target = entry * (1 + reward_ratio * risk_percent / 100)
                    expected_stop = entry * (1 - risk_percent / 100)
                    print(f"   Expected Target (Bullish): ${expected_target:.2f}")
                    print(f"   Expected Stop (Bullish): ${expected_stop:.2f}")
                else:
                    expected_target = entry * (1 - reward_ratio * risk_percent / 100)
                    expected_stop = entry * (1 + risk_percent / 100)
                    print(f"   Expected Target (Bearish): ${expected_target:.2f}")
                    print(f"   Expected Stop (Bearish): ${expected_stop:.2f}")
                
                # Check if calculations match
                target_match = abs(signal.target_price - expected_target) < 0.01
                stop_match = abs(signal.stop_loss - expected_stop) < 0.01
                
                print(f"\n✅ Validation:")
                print(f"   Target Price Match: {'✅' if target_match else '❌'}")
                print(f"   Stop Loss Match: {'✅' if stop_match else '❌'}")
                
                if not target_match or not stop_match:
                    print(f"   🚨 PRICE CALCULATION ERROR DETECTED!")
                    
                    # Check if signal direction is correct
                    print(f"\n🔍 Signal Direction Analysis:")
                    print(f"   EMA5 Trend: {signal.ema5_uptrend}")
                    print(f"   EMA8 Trend: {signal.ema8_uptrend}")
                    print(f"   Momentum Trend: {signal.momentum_uptrend}")
                    print(f"   Histogram Current: {signal.histogram_current:.4f}")
                    
                    # Determine expected direction
                    if signal.ema5_uptrend and signal.ema8_uptrend and signal.momentum_uptrend:
                        expected_direction = 'bullish_momentum'
                    else:
                        expected_direction = 'bearish_momentum'
                    
                    print(f"   Expected Direction: {expected_direction}")
                    print(f"   Actual Direction: {signal.signal_type}")
                    
                    if expected_direction != signal.signal_type:
                        print(f"   🚨 SIGNAL DIRECTION MISMATCH!")
                
            else:
                print(f"❌ No signal detected for {symbol}")
                
        except Exception as e:
            print(f"❌ Error analyzing {symbol}: {e}")
    
    print(f"\n🔍 Scanner Configuration:")
    print(f"   Default Risk Percent: {scanner.default_risk_percent}%")
    print(f"   Default Reward Ratio: {scanner.default_reward_ratio}:1")
    print(f"   Min Confidence: {scanner.min_confidence_threshold}")
    print(f"   Pattern Sensitivity: {scanner.pattern_sensitivity}")

if __name__ == "__main__":
    asyncio.run(debug_signal_prices())
