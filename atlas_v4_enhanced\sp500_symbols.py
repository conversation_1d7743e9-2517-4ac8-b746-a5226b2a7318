"""
S&P 500 Symbol List for A.T.L.A.S. Trading System
Comprehensive list of S&P 500 stocks for market scanning
"""

# S&P 500 symbols (updated list)
SP500_SYMBOLS = [
    # Technology
    "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE",
    "CRM", "ORCL", "INTC", "AMD", "QCOM", "AVGO", "TXN", "AMAT", "LRCX", "KLAC",
    "MRVL", "MU", "ADI", "MCHP", "SNPS", "CDNS", "FTNT", "PANW", "CRWD", "ZS",
    "OKTA", "DDOG", "NET", "SNOW", "PLTR", "COIN", "SQ", "PYPL", "SHOP", "UBER",
    "LYFT", "ABNB", "DASH", "ZM", "DOCU", "ROKU", "SPOT", "TWLO", "WORK", "TEAM",
    
    # Healthcare & Pharmaceuticals
    "JNJ", "PFE", "UNH", "ABBV", "MRK", "TMO", "ABT", "DHR", "BMY", "AMGN",
    "GILD", "VRTX", "REGN", "BIIB", "ILMN", "MRNA", "BNTX", "ZTS", "ELV", "CVS",
    "CI", "HUM", "ANTM", "CNC", "MOH", "UHS", "HCA", "THC", "DVA", "DGX",
    "LH", "IQV", "A", "RMD", "ISRG", "SYK", "BSX", "MDT", "EW", "HOLX",
    
    # Financial Services
    "BRK.B", "JPM", "BAC", "WFC", "GS", "MS", "C", "AXP", "BLK", "SPGI",
    "CME", "ICE", "NDAQ", "MCO", "COF", "USB", "TFC", "PNC", "SCHW", "CB",
    "MMC", "AON", "AJG", "BRO", "PGR", "TRV", "ALL", "AIG", "MET", "PRU",
    "AFL", "L", "LNC", "PFG", "TMK", "RJF", "SIVB", "FITB", "HBAN", "RF",
    
    # Consumer Discretionary
    "AMZN", "TSLA", "HD", "MCD", "NKE", "SBUX", "LOW", "TJX", "BKNG", "ABNB",
    "GM", "F", "UBER", "LYFT", "CCL", "RCL", "NCLH", "MAR", "HLT", "MGM",
    "WYNN", "LVS", "CZR", "PENN", "DRI", "CMG", "QSR", "YUM", "DPZ", "DNKN",
    "MHK", "WHR", "LEN", "DHI", "PHM", "NVR", "TOL", "KBH", "MTH", "TMHC",
    
    # Consumer Staples
    "PG", "KO", "PEP", "WMT", "COST", "MDLZ", "GIS", "K", "CPB", "CAG",
    "HSY", "SJM", "HRL", "TSN", "TYSON", "ADM", "BG", "CF", "MOS", "FMC",
    "CL", "KMB", "CHD", "CLX", "EL", "COTY", "TAP", "STZ", "DEO", "BF.B",
    
    # Energy
    "XOM", "CVX", "COP", "EOG", "SLB", "PSX", "VLO", "MPC", "HES", "DVN",
    "FANG", "APA", "OXY", "BKR", "HAL", "NOV", "FTI", "HP", "RIG", "VAL",
    "KMI", "OKE", "EPD", "ET", "WMB", "TRGP", "ONEOK", "MPLX", "PAA", "EQT",
    
    # Industrials
    "BA", "CAT", "GE", "MMM", "HON", "UPS", "RTX", "LMT", "NOC", "GD",
    "LHX", "TXT", "ETN", "EMR", "ITW", "PH", "CMI", "DE", "FDX", "UNP",
    "CSX", "NSC", "KSU", "CP", "CNI", "ODFL", "CHRW", "EXPD", "JBHT", "KNX",
    
    # Materials
    "LIN", "APD", "ECL", "SHW", "FCX", "NEM", "GOLD", "AA", "X", "CLF",
    "NUE", "STLD", "RS", "MLM", "VMC", "EMN", "DD", "DOW", "LYB", "CF",
    
    # Real Estate
    "AMT", "PLD", "CCI", "EQIX", "PSA", "EXR", "AVB", "EQR", "UDR", "CPT",
    "MAA", "ESS", "AIV", "BXP", "VTR", "WELL", "PEAK", "O", "STOR", "CXW",
    
    # Utilities
    "NEE", "DUK", "SO", "D", "AEP", "EXC", "XEL", "SRE", "PEG", "ED",
    "FE", "ETR", "ES", "DTE", "PPL", "AES", "LNT", "NI", "EVRG", "CMS",
    
    # Communication Services
    "GOOGL", "GOOG", "META", "NFLX", "DIS", "CMCSA", "VZ", "T", "TMUS", "CHTR",
    "DISH", "FOXA", "FOX", "PARA", "WBD", "NWSA", "NWS", "NYT", "TWTR", "SNAP",
    
    # REITs and Others
    "BRK.A", "BRK.B", "SPY", "QQQ", "IWM", "VTI", "VOO", "VEA", "VWO", "AGG"
]

# Core S&P 500 symbols (most liquid and actively traded)
CORE_SP500_SYMBOLS = [
    "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE", "CRM",
    "ORCL", "INTC", "AMD", "QCOM", "AVGO", "TXN", "AMAT", "MU", "PYPL", "SQ",
    "JNJ", "PFE", "UNH", "ABBV", "MRK", "TMO", "ABT", "DHR", "BMY", "AMGN",
    "BRK.B", "JPM", "BAC", "WFC", "GS", "MS", "C", "AXP", "BLK", "SPGI",
    "HD", "MCD", "NKE", "SBUX", "LOW", "TJX", "BKNG", "GM", "F", "MAR",
    "PG", "KO", "PEP", "WMT", "COST", "MDLZ", "GIS", "K", "CPB", "CAG",
    "XOM", "CVX", "COP", "EOG", "SLB", "PSX", "VLO", "MPC", "HES", "DVN",
    "BA", "CAT", "GE", "MMM", "HON", "UPS", "RTX", "LMT", "NOC", "GD",
    "LIN", "APD", "ECL", "SHW", "FCX", "NEM", "NUE", "STLD", "MLM", "VMC",
    "NEE", "DUK", "SO", "D", "AEP", "EXC", "XEL", "SRE", "PEG", "ED"
]

# High-volume trading symbols (most suitable for day trading)
HIGH_VOLUME_SYMBOLS = [
    "SPY", "QQQ", "IWM", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA",
    "NFLX", "AMD", "INTC", "PYPL", "SQ", "UBER", "LYFT", "ABNB", "COIN", "PLTR",
    "JPM", "BAC", "WFC", "GS", "XOM", "CVX", "F", "GM", "HD", "WMT"
]

def get_sp500_symbols() -> list:
    """Get the full S&P 500 symbol list"""
    return SP500_SYMBOLS.copy()

def get_core_sp500_symbols() -> list:
    """Get core S&P 500 symbols (most liquid)"""
    return CORE_SP500_SYMBOLS.copy()

def get_high_volume_symbols() -> list:
    """Get high-volume trading symbols"""
    return HIGH_VOLUME_SYMBOLS.copy()

def get_symbols_by_sector(sector: str) -> list:
    """Get symbols by sector (simplified categorization)"""
    sector_map = {
        'technology': ["AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE"],
        'healthcare': ["JNJ", "PFE", "UNH", "ABBV", "MRK", "TMO", "ABT", "DHR", "BMY", "AMGN"],
        'financial': ["BRK.B", "JPM", "BAC", "WFC", "GS", "MS", "C", "AXP", "BLK", "SPGI"],
        'consumer': ["HD", "MCD", "NKE", "SBUX", "LOW", "TJX", "BKNG", "PG", "KO", "PEP"],
        'energy': ["XOM", "CVX", "COP", "EOG", "SLB", "PSX", "VLO", "MPC", "HES", "DVN"],
        'industrial': ["BA", "CAT", "GE", "MMM", "HON", "UPS", "RTX", "LMT", "NOC", "GD"]
    }
    return sector_map.get(sector.lower(), [])

# Symbol validation
def is_valid_sp500_symbol(symbol: str) -> bool:
    """Check if symbol is in S&P 500"""
    return symbol.upper() in SP500_SYMBOLS

def get_symbol_count() -> dict:
    """Get symbol counts by category"""
    return {
        'total_sp500': len(SP500_SYMBOLS),
        'core_sp500': len(CORE_SP500_SYMBOLS),
        'high_volume': len(HIGH_VOLUME_SYMBOLS)
    }
