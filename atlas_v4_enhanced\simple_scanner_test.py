#!/usr/bin/env python3
"""
Simple test to verify the scanner fixes work
"""

import asyncio
import sys
import os

# Add the current directory to path
sys.path.append(os.path.dirname(__file__))

from atlas_lee_method import LeeMethodScanner

async def simple_test():
    """Simple test of the fixed scanner"""
    print("🔧 Simple Scanner Fix Test")
    print("=" * 30)
    
    # Initialize scanner
    print("1. Initializing scanner...")
    scanner = LeeMethodScanner()
    
    # Configure for flexible patterns
    print("2. Configuring flexible patterns...")
    scanner.configure_pattern_sensitivity(
        use_flexible_patterns=True,
        min_confidence_threshold=0.3,
        pattern_sensitivity=0.8,
        allow_weak_signals=True
    )
    
    # Test with a few symbols
    print("3. Testing with sample symbols...")
    test_symbols = ['AAPL', 'MSFT', 'GOOGL']
    
    signals_found = 0
    
    for symbol in test_symbols:
        print(f"   Testing {symbol}...")
        try:
            signal = await scanner.scan_symbol(symbol)
            if signal:
                signals_found += 1
                print(f"   ✅ {symbol}: Found signal! Confidence: {signal.confidence:.2f}")
            else:
                print(f"   ❌ {symbol}: No signal")
        except Exception as e:
            print(f"   ❌ {symbol}: Error - {e}")
    
    print(f"\n4. Results: {signals_found}/{len(test_symbols)} signals found")
    
    if signals_found > 0:
        print("🎉 SUCCESS: Scanner is working!")
    else:
        print("⚠️  No signals found - may need further adjustment")
    
    return signals_found

if __name__ == "__main__":
    asyncio.run(simple_test())
